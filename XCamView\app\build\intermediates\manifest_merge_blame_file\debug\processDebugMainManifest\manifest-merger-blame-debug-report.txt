1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.touptek.xcamview"
4    android:sharedUserId="android.uid.system"
5    android:versionCode="5002"
6    android:versionName="14" >
7
8    <uses-sdk
9        android:minSdkVersion="31"
10        android:targetSdkVersion="34" />
11
12    <uses-permission android:name="android.permission.CAMERA" />
12-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:7:5-65
12-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:7:22-62
13    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
13-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:8:5-81
13-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:8:22-78
14    <uses-permission android:name="android.permission.RECORD_AUDIO" />
14-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:9:5-71
14-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:9:22-68
15    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
15-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:10:5-80
15-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:10:22-77
16    <uses-permission android:name="android.permission.MANAGE_EXTERNAL_STORAGE" /> <!-- 适用于Android 11及以上 -->
16-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:11:5-82
16-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:11:22-79
17    <uses-permission android:name="android.permission.VIBRATE" />
17-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:12:5-66
17-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:12:22-63
18
19    <permission
19-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\be4ee3b3d380c351331d5de220cf8d41\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
20        android:name="com.touptek.xcamview.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
20-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\be4ee3b3d380c351331d5de220cf8d41\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
21        android:protectionLevel="signature" />
21-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\be4ee3b3d380c351331d5de220cf8d41\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
22
23    <uses-permission android:name="com.touptek.xcamview.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
23-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\be4ee3b3d380c351331d5de220cf8d41\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
23-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\be4ee3b3d380c351331d5de220cf8d41\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
24
25    <application
25-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:14:5-49:19
26        android:allowBackup="true"
26-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:15:9-35
27        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
27-->[androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\be4ee3b3d380c351331d5de220cf8d41\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
28        android:dataExtractionRules="@xml/data_extraction_rules"
28-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:16:9-65
29        android:debuggable="true"
30        android:extractNativeLibs="false"
31        android:fullBackupContent="@xml/backup_rules"
31-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:17:9-54
32        android:icon="@mipmap/ic_launcher"
32-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:18:9-43
33        android:label="XCamView"
33-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:19:9-33
34        android:roundIcon="@mipmap/ic_launcher_round"
34-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:20:9-54
35        android:supportsRtl="true"
35-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:21:9-35
36        android:testOnly="true"
37        android:theme="@style/Theme.AppCompat.DayNight.NoActionBar" >
37-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:22:9-68
38
39        <!-- android:theme="@style/Theme.AppCompat.DayNight.NoActionBar" -->
40        <!-- android:theme="@style/Theme.AppCompat.DayNight" -->
41
42
43        <!-- 设置MainActivity为启动Activity -->
44        <activity
44-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:30:9-43:20
45            android:name="com.touptek.xcamview.activity.MainActivity"
45-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:31:13-70
46            android:configChanges="keyboard|keyboardHidden|screenSize|smallestScreenSize|screenLayout|orientation"
46-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:32:13-115
47            android:exported="true"
47-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:33:13-36
48            android:launchMode="singleTask"
48-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:34:13-44
49            android:resizeableActivity="true"
49-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:35:13-46
50            android:screenOrientation="unspecified"
50-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:36:13-52
51            android:supportsPictureInPicture="true" >
51-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:37:13-52
52            <intent-filter>
52-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:38:13-42:29
53                <action android:name="android.intent.action.MAIN" />
53-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:39:17-69
53-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:39:25-66
54
55                <category android:name="android.intent.category.LAUNCHER" />
55-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:40:17-77
55-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:40:27-74
56                <category android:name="android.intent.category.DEFAULT" />
56-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:41:17-76
56-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:41:27-73
57            </intent-filter>
58        </activity>
59        <activity android:name="com.touptek.xcamview.activity.videomanagement.TpVideoEncoderActivity" />
59-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:45:9-85
59-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:45:19-82
60        <activity android:name="com.touptek.xcamview.activity.videomanagement.TpVideoDecoderActivity" />
60-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:46:9-85
60-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:46:19-82
61        <activity android:name="com.touptek.xcamview.activity.browse.TpVideoBrowse" />
61-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:47:9-67
61-->C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:47:19-64
62
63        <!-- 图片对比功能Activity -->
64        <activity
64-->[CodecUtils.aar] C:\Users\<USER>\.gradle\caches\transforms-4\b0f775f00dbcf1da11648a9d24df52d2\transformed\CodecUtils\AndroidManifest.xml:10:9-14:66
65            android:name="com.touptek.ui.compare.TpImageCompareActivity"
65-->[CodecUtils.aar] C:\Users\<USER>\.gradle\caches\transforms-4\b0f775f00dbcf1da11648a9d24df52d2\transformed\CodecUtils\AndroidManifest.xml:11:13-73
66            android:exported="false"
66-->[CodecUtils.aar] C:\Users\<USER>\.gradle\caches\transforms-4\b0f775f00dbcf1da11648a9d24df52d2\transformed\CodecUtils\AndroidManifest.xml:12:13-37
67            android:screenOrientation="landscape"
67-->[CodecUtils.aar] C:\Users\<USER>\.gradle\caches\transforms-4\b0f775f00dbcf1da11648a9d24df52d2\transformed\CodecUtils\AndroidManifest.xml:13:13-50
68            android:theme="@style/Theme.AppCompat.NoActionBar" />
68-->[CodecUtils.aar] C:\Users\<USER>\.gradle\caches\transforms-4\b0f775f00dbcf1da11648a9d24df52d2\transformed\CodecUtils\AndroidManifest.xml:14:13-63
69        <activity
69-->[CodecUtils.aar] C:\Users\<USER>\.gradle\caches\transforms-4\b0f775f00dbcf1da11648a9d24df52d2\transformed\CodecUtils\AndroidManifest.xml:15:9-19:66
70            android:name="com.touptek.ui.compare.TpImageCompareTripleActivity"
70-->[CodecUtils.aar] C:\Users\<USER>\.gradle\caches\transforms-4\b0f775f00dbcf1da11648a9d24df52d2\transformed\CodecUtils\AndroidManifest.xml:16:13-79
71            android:exported="false"
71-->[CodecUtils.aar] C:\Users\<USER>\.gradle\caches\transforms-4\b0f775f00dbcf1da11648a9d24df52d2\transformed\CodecUtils\AndroidManifest.xml:17:13-37
72            android:screenOrientation="landscape"
72-->[CodecUtils.aar] C:\Users\<USER>\.gradle\caches\transforms-4\b0f775f00dbcf1da11648a9d24df52d2\transformed\CodecUtils\AndroidManifest.xml:18:13-50
73            android:theme="@style/Theme.AppCompat.NoActionBar" />
73-->[CodecUtils.aar] C:\Users\<USER>\.gradle\caches\transforms-4\b0f775f00dbcf1da11648a9d24df52d2\transformed\CodecUtils\AndroidManifest.xml:19:13-63
74        <activity
74-->[CodecUtils.aar] C:\Users\<USER>\.gradle\caches\transforms-4\b0f775f00dbcf1da11648a9d24df52d2\transformed\CodecUtils\AndroidManifest.xml:20:9-24:66
75            android:name="com.touptek.ui.compare.TpImageCompareMultiActivity"
75-->[CodecUtils.aar] C:\Users\<USER>\.gradle\caches\transforms-4\b0f775f00dbcf1da11648a9d24df52d2\transformed\CodecUtils\AndroidManifest.xml:21:13-78
76            android:exported="false"
76-->[CodecUtils.aar] C:\Users\<USER>\.gradle\caches\transforms-4\b0f775f00dbcf1da11648a9d24df52d2\transformed\CodecUtils\AndroidManifest.xml:22:13-37
77            android:screenOrientation="landscape"
77-->[CodecUtils.aar] C:\Users\<USER>\.gradle\caches\transforms-4\b0f775f00dbcf1da11648a9d24df52d2\transformed\CodecUtils\AndroidManifest.xml:23:13-50
78            android:theme="@style/Theme.AppCompat.NoActionBar" />
78-->[CodecUtils.aar] C:\Users\<USER>\.gradle\caches\transforms-4\b0f775f00dbcf1da11648a9d24df52d2\transformed\CodecUtils\AndroidManifest.xml:24:13-63
79
80        <provider
80-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\912c807adf5a57eb3e906bf09edd6568\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
81            android:name="androidx.startup.InitializationProvider"
81-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\912c807adf5a57eb3e906bf09edd6568\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
82            android:authorities="com.touptek.xcamview.androidx-startup"
82-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\912c807adf5a57eb3e906bf09edd6568\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
83            android:exported="false" >
83-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\912c807adf5a57eb3e906bf09edd6568\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
84            <meta-data
84-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\912c807adf5a57eb3e906bf09edd6568\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
85                android:name="androidx.emoji2.text.EmojiCompatInitializer"
85-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\912c807adf5a57eb3e906bf09edd6568\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
86                android:value="androidx.startup" />
86-->[androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\912c807adf5a57eb3e906bf09edd6568\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
87            <meta-data
87-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\2659dca782c3902a5a89c540359c7180\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
88                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
88-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\2659dca782c3902a5a89c540359c7180\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
89                android:value="androidx.startup" />
89-->[androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\2659dca782c3902a5a89c540359c7180\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
90            <meta-data
90-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
91                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
91-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
92                android:value="androidx.startup" />
92-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
93        </provider>
94
95        <receiver
95-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
96            android:name="androidx.profileinstaller.ProfileInstallReceiver"
96-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
97            android:directBootAware="false"
97-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
98            android:enabled="true"
98-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
99            android:exported="true"
99-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
100            android:permission="android.permission.DUMP" >
100-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
101            <intent-filter>
101-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
102                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
102-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
102-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
103            </intent-filter>
104            <intent-filter>
104-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
105                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
105-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
105-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
106            </intent-filter>
107            <intent-filter>
107-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
108                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
108-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
108-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
109            </intent-filter>
110            <intent-filter>
110-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
111                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
111-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
111-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
112            </intent-filter>
113        </receiver>
114    </application>
115
116</manifest>
