package com.android.rockchip.camera2.integrated.browser;


import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.Canvas;
import android.graphics.Matrix;
import android.graphics.RectF;
import android.util.AttributeSet;
import android.util.Log;
import android.view.GestureDetector;
import android.view.MotionEvent;
import android.view.ScaleGestureDetector;
import android.view.TextureView;
import android.view.ViewConfiguration;


/**
 * TpTextureView - 与TpImageView完全一致的可缩放TextureView
 * 
 * 提供与TpImageView完全相同的缩放和平移体验：
 * - 硬边界限制，无阻尼效果
 * - 智能双击缩放：始终回到能让整个视频完全显示在屏幕上的最小缩放比例
 * - 双指缩放时焦点稳定，无跳跃感
 * - 解决手势冲突问题
 * 
 * 主要功能：
 * - 双指缩放（焦点稳定）
 * - 单指平移（硬边界限制）
 * - 双击缩放切换
 * - 小视频自动居中
 * - 大视频边缘对齐
 * - 惯性滑动
 */
public class TpTextureView extends TextureView {
    private static final String TAG = "TpTextureView";

    // 缩放相关（修正为FIT_CENTER逻辑）
    private float currentScale = 1f;
    private float baseScale = 1f;        // 统一的基础缩放（用于手势计算）
    private float baseScaleX = 1f;       // X轴基础缩放
    private float baseScaleY = 1f;       // Y轴基础缩放
    private float dynamicMinScale = 0.3f; // 将在初始化时设置为baseScale
    private static final float MAX_SCALE = 5.0f;
    private static final float SCALE_SENSITIVITY = 3.0f; // 缩放敏感度增强
    
    // 缩放优化参数（与TpImageView一致）
    private static final float MIN_SCALE_SPAN = 10f; // 降低触发阈值
    private static final float CUSTOM_SCALE_THRESHOLD = 3f; // 自定义缩放触发阈值

    // 矩阵和手势（与TpImageView一致）
    private final Matrix matrix = new Matrix();
    private final Matrix savedMatrix = new Matrix();
    private final Matrix baseMatrix = new Matrix();
    private ScaleGestureDetector scaleDetector;
    private GestureDetector gestureDetector;
    
    // 状态标志（与TpImageView一致）
    private boolean isInitialized = false;
    private boolean isScaling = false;
    private boolean isCustomScaling = false; // 自定义缩放状态
    private boolean isZoomEnabled = true;
    
    // 视图和视频尺寸
    private int viewWidth = 0;
    private int viewHeight = 0;
    private int videoWidth = 0;
    private int videoHeight = 0;
    


    // 自定义缩放检测变量（与TpImageView一致）
    private float lastDistance = -1f;
    private float lastFocusX = 0f;
    private float lastFocusY = 0f;
    
    // 触摸相关
    private int touchSlop;

    // 监听器
    private OnSingleTapListener onSingleTapListener;
    private OnMatrixChangeListener matrixChangeListener;

    // 图片显示相关
    private Bitmap currentBitmap;
    private boolean isImageMode = false;
    private String currentImagePath;

    // 图片模式的简单变换系统（完全按照testVieoImage项目）
    private Matrix imageTransformMatrix;
    private float imageScaleFactor = 1.0f;
    private float imageTranslateX = 0f;
    private float imageTranslateY = 0f;

    // 图片模式的手势检测器（独立于视频）
    private ScaleGestureDetector imageScaleDetector;
    private GestureDetector imageGestureDetector;

    // Matrix同步相关
    private TpTextureView partnerView;
    private boolean isSyncing = false;

    public interface OnSingleTapListener {
        void onSingleTap(MotionEvent event);
    }

    public interface OnMatrixChangeListener {
        void onMatrixChanged();
    }

    public TpTextureView(Context context) {
        this(context, null);
    }

    public TpTextureView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public TpTextureView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(context);
    }

    private void init(Context context) {
        touchSlop = ViewConfiguration.get(context).getScaledTouchSlop();
        setupGestureDetectors();
        setupImageGestureDetectors(); // 为图片模式创建独立的手势检测器
        setupTouchListener();

        // 初始化图片变换矩阵
        imageTransformMatrix = new Matrix();
        
        // 配置缩放检测器的敏感度
        configureScaleDetectorSensitivity();
        
        Log.d(TAG, "TpTextureView initialized");
    }

    @Override
    protected void onSizeChanged(int w, int h, int oldw, int oldh) {
        super.onSizeChanged(w, h, oldw, oldh);
        viewWidth = w;
        viewHeight = h;
        Log.d(TAG, "View size changed: " + w + "x" + h);
        
        // 如果视频尺寸已设置，更新Matrix
        if (videoWidth > 0 && videoHeight > 0) {
            updateMatrix();
        }
    }

    /**
     * 设置视频尺寸（必须调用此方法才能正确工作）
     */
    public void setVideoSize(int width, int height) {
        this.videoWidth = width;
        this.videoHeight = height;
        Log.d(TAG, "Video size set: " + width + "x" + height);
        
        // 如果视图尺寸已知，立即更新Matrix
        if (viewWidth > 0 && viewHeight > 0) {
            post(this::updateMatrix);
        }
    }

    /**
     * 更新Matrix（修正为FIT_CENTER逻辑，解决视频压缩问题）
     * 注意：此方法仅用于视频模式，图片模式有独立的变换系统
     */
    private void updateMatrix() {
        // 图片模式不使用此方法，避免冲突
        if (isImageMode) {
            Log.d(TAG, "🖼️ 图片模式跳过updateMatrix，使用独立变换系统");
            return;
        }

        if (viewWidth <= 0 || viewHeight <= 0 || videoWidth <= 0 || videoHeight <= 0) {
            Log.w(TAG, "❌ Invalid dimensions: view=" + viewWidth + "x" + viewHeight +
                      ", video=" + videoWidth + "x" + videoHeight);
            return;
        }

        Log.d(TAG, "🔧 开始FIT_CENTER Matrix计算:");
        Log.d(TAG, "  TextureView尺寸: " + viewWidth + "x" + viewHeight);

        String contentType = isImageMode ? "图片" : "视频";
        Log.d(TAG, "  " + contentType + "尺寸: " + videoWidth + "x" + videoHeight);

        // 计算宽高比
        float textureViewRatio = (float) viewWidth / viewHeight;  // 容器比例 ≈ 1.0
        float videoRatio = (float) videoWidth / videoHeight;      // 视频比例 ≈ 1.78 (16:9)

        Log.d(TAG, "  TextureView比例: " + String.format("%.3f", textureViewRatio));
        Log.d(TAG, "  视频比例: " + String.format("%.3f", videoRatio));

        // 🎯 关键修正：使用正确的FIT_CENTER逻辑
        if (videoRatio > textureViewRatio) {
            // 视频更宽，需要在Y轴方向缩小来保持比例
            baseScaleX = 1.0f;  // X轴保持满宽度
            baseScaleY = textureViewRatio / videoRatio;  // Y轴缩小到正确比例
            Log.d(TAG, "  视频更宽，X轴=1.0，Y轴=" + String.format("%.3f", baseScaleY));
        } else {
            // 视频更高，需要在X轴方向缩小来保持比例
            baseScaleX = videoRatio / textureViewRatio;  // X轴缩小到正确比例
            baseScaleY = 1.0f;  // Y轴保持满高度
            Log.d(TAG, "  视频更高，X轴=" + String.format("%.3f", baseScaleX) + "，Y轴=1.0");
        }

        // 统一的baseScale用于手势计算（取较小值确保完整显示）
        baseScale = Math.min(baseScaleX, baseScaleY);
        dynamicMinScale = baseScale;
        currentScale = baseScale;

        Log.d(TAG, "  最终缩放: scaleX=" + baseScaleX + ", scaleY=" + baseScaleY);
        Log.d(TAG, "  统一baseScale=" + baseScale + " (用于手势计算)");

        // 设置基础Matrix（使用分离的X/Y轴缩放）
        baseMatrix.reset();
        baseMatrix.postScale(baseScaleX, baseScaleY);

        // 居中平移（基于缩放后的尺寸）
        float scaledWidth = viewWidth * baseScaleX;
        float scaledHeight = viewHeight * baseScaleY;
        float translateX = (viewWidth - scaledWidth) / 2f;
        float translateY = (viewHeight - scaledHeight) / 2f;

        baseMatrix.postTranslate(translateX, translateY);

        Log.d(TAG, "  居中平移: X=" + String.format("%.1f", translateX) +
                   ", Y=" + String.format("%.1f", translateY));

        // 初始化当前Matrix
        matrix.set(baseMatrix);
        updateTextureMatrix(matrix);

        isInitialized = true;

        // 如果是图片模式且图片已加载，立即绘制
        if (isImageMode && currentBitmap != null) {
            post(() -> drawImageOnCanvas());
        }

        // 验证最终效果
        float actualDisplayWidth = viewWidth * baseScaleX;
        float actualDisplayHeight = viewHeight * baseScaleY;
        float actualRatio = actualDisplayWidth / actualDisplayHeight;

        Log.d(TAG, "✅ Matrix初始化完成:");
        Log.d(TAG, "  实际显示尺寸: " + String.format("%.1f", actualDisplayWidth) +
                   "x" + String.format("%.1f", actualDisplayHeight));
        Log.d(TAG, "  实际显示比例: " + String.format("%.2f", actualRatio) + ":1");
        Log.d(TAG, "  目标比例: " + String.format("%.2f", videoRatio) + ":1 (16:9)");

        if (Math.abs(actualRatio - videoRatio) < 0.1f) {
            Log.d(TAG, "  ✅ 比例正确！视频将以16:9比例显示");
        } else {
            Log.w(TAG, "  ⚠️ 比例可能有偏差");
        }
    }

    /**
     * 应用Matrix变换到TextureView
     */
    private void updateTextureMatrix(Matrix newMatrix) {
        if (newMatrix == null) return;

        setTransform(newMatrix);
        invalidate(); // 强制刷新

        // 刷新图片显示（如果是图片模式）
        refreshImageDisplay();

        // 同步到伙伴视图（防循环同步）
        syncToPartnerView();

        // 通知Matrix变化监听器
        if (matrixChangeListener != null) {
            matrixChangeListener.onMatrixChanged();
        }
    }

    /**
     * 同步缩放手势到伙伴视图（保留手势中心点信息，修复大倍数缩放问题）
     */
    private void syncScaleGestureToPartner(float scaleFactor, float focusX, float focusY) {
        if (partnerView != null && !isSyncing && !isImageMode && partnerView.isImageMode) {
            isSyncing = true;
            try {
                // 改进的坐标系转换：使用双精度计算提高精度
                double scaleX = (double) partnerView.getWidth() / getWidth();
                double scaleY = (double) partnerView.getHeight() / getHeight();
                float partnerFocusX = (float) (focusX * scaleX);
                float partnerFocusY = (float) (focusY * scaleY);

                // 应用缩放到图片，保持焦点位置
                float oldScale = partnerView.imageScaleFactor;
                float targetScale = oldScale * scaleFactor;

                // 检查是否会超出缩放限制
                boolean isAtMaxScale = oldScale >= 4.99f; // 接近最大值时停止同步
                boolean isAtMinScale = oldScale <= 1.01f; // 接近最小值时停止同步

                // 应用图片的缩放限制
                float newScale = Math.max(1.0f, Math.min(targetScale, 5.0f));

                // 关键修复：当达到缩放限制时，停止同步以防止平移偏差
                if ((isAtMaxScale && scaleFactor > 1.0f) || (isAtMinScale && scaleFactor < 1.0f)) {
                    Log.d(TAG, "📹 图片已达到缩放限制，停止同步: scale=" + String.format("%.3f", oldScale) +
                               ", scaleFactor=" + String.format("%.3f", scaleFactor));
                    return;
                }

                if (Math.abs(newScale - oldScale) > 0.001f) { // 添加精度阈值
                    float actualFactor = newScale / oldScale;

                    // 焦点缩放：保持手势中心位置不变
                    float offsetX = partnerFocusX - partnerView.imageTranslateX;
                    float offsetY = partnerFocusY - partnerView.imageTranslateY;

                    partnerView.imageScaleFactor = newScale;
                    partnerView.imageTranslateX = partnerFocusX - offsetX * actualFactor;
                    partnerView.imageTranslateY = partnerFocusY - offsetY * actualFactor;

                    partnerView.updateImageTransformMatrix();
                    partnerView.drawImageOnCanvas();

                    Log.d(TAG, "📹 手势缩放已同步到图片: scale=" + String.format("%.3f", oldScale) +
                               " -> " + String.format("%.3f", newScale) +
                               ", focus=(" + String.format("%.1f", focusX) + "," + String.format("%.1f", focusY) +
                               ") -> (" + String.format("%.1f", partnerFocusX) + "," + String.format("%.1f", partnerFocusY) + ")");
                }
            } finally {
                isSyncing = false;
            }
        }
    }

    /**
     * 同步平移手势到伙伴视图（改进大倍数缩放时的精度）
     */
    private void syncTranslateGestureToPartner(float deltaX, float deltaY) {
        if (partnerView != null && !isSyncing && !isImageMode && partnerView.isImageMode) {
            isSyncing = true;
            try {
                // 改进的坐标系转换：使用双精度计算
                double scaleX = (double) partnerView.getWidth() / getWidth();
                double scaleY = (double) partnerView.getHeight() / getHeight();

                float scaledDeltaX = (float) (deltaX * scaleX);
                float scaledDeltaY = (float) (deltaY * scaleY);

                // 应用平移到图片
                partnerView.imageTranslateX += scaledDeltaX;
                partnerView.imageTranslateY += scaledDeltaY;

                partnerView.updateImageTransformMatrix();
                partnerView.drawImageOnCanvas(); // 直接绘制，避免触发applySyncTransform的循环

                Log.d(TAG, "📹 平移手势已同步到图片: delta=(" + String.format("%.1f", deltaX) +
                           "," + String.format("%.1f", deltaY) + ") -> (" +
                           String.format("%.1f", scaledDeltaX) + "," + String.format("%.1f", scaledDeltaY) + ")");
            } finally {
                isSyncing = false;
            }
        }
    }

    /**
     * 同步Matrix到伙伴视图（仅用于视频模式）
     */
    private void syncToPartnerView() {
        if (partnerView != null && !isSyncing && !isImageMode) {
            isSyncing = true;
            try {
                if (partnerView.isImageMode) {
                    // 伙伴视图是图片模式，需要提取相对于baseMatrix的用户变换
                    Matrix userTransform = new Matrix();
                    Matrix inverseBase = new Matrix();

                    if (baseMatrix.invert(inverseBase)) {
                        userTransform.setConcat(matrix, inverseBase);

                        float[] values = new float[9];
                        userTransform.getValues(values);

                        // 提取用户的缩放和平移参数
                        float userScale = values[Matrix.MSCALE_X];
                        float userTranslateX = values[Matrix.MTRANS_X];
                        float userTranslateY = values[Matrix.MTRANS_Y];

                        // 关键修复：应用与TpImageView相同的最小缩放限制
                        // 图片的最小缩放应该是1.0f（相当于TpImageView的baseScale）
                        float constrainedScale = Math.max(1.0f, Math.min(userScale, 5.0f));

                        // 修复平移偏差：当缩放被限制时，按比例调整平移
                        float scaleRatio = constrainedScale / userScale;
                        float adjustedTranslateX = userTranslateX * scaleRatio;
                        float adjustedTranslateY = userTranslateY * scaleRatio;

                        // 同步到图片变换系统
                        partnerView.imageScaleFactor = constrainedScale;
                        partnerView.imageTranslateX = adjustedTranslateX;
                        partnerView.imageTranslateY = adjustedTranslateY;
                        partnerView.updateImageTransformMatrix();
                        partnerView.drawImageOnCanvas();

                        Log.d(TAG, "📹 视频用户变换已同步到图片: userScale=" + String.format("%.3f", userScale) +
                                   " -> constrainedScale=" + String.format("%.3f", constrainedScale) +
                                   ", translate=(" + String.format("%.1f", userTranslateX) + "," + String.format("%.1f", userTranslateY) +
                                   ") -> (" + String.format("%.1f", adjustedTranslateX) + "," + String.format("%.1f", adjustedTranslateY) +
                                   "), scaleRatio=" + String.format("%.3f", scaleRatio));
                    } else {
                        Log.w(TAG, "📹 无法计算baseMatrix逆矩阵，跳过同步");
                    }
                } else {
                    // 伙伴视图也是视频模式
                    Matrix currentMatrix = new Matrix(matrix);
                    partnerView.applyExternalMatrix(currentMatrix);
                    Log.d(TAG, "📹 视频Matrix已同步到视频伙伴视图");
                }
            } finally {
                isSyncing = false;
            }
        }
    }

    /**
     * 设置手势检测器（与TpImageView完全一致）
     */
    private void setupGestureDetectors() {
        // 缩放手势检测器
        scaleDetector = new ScaleGestureDetector(getContext(), new ScaleGestureDetector.SimpleOnScaleGestureListener() {
            @Override
            public boolean onScaleBegin(ScaleGestureDetector detector) {
                Log.d(TAG, "🔍 Scale begin - span: " + detector.getCurrentSpan());
                isScaling = true;
                return true;
            }

            @Override
            public boolean onScale(ScaleGestureDetector detector) {
                if (isImageMode) {
                    // 图片模式：焦点缩放处理（修复边缘缩放问题）
                    float scaleFactor = detector.getScaleFactor();
                    float enhancedFactor = 1f + (scaleFactor - 1f) * SCALE_SENSITIVITY;
                    float oldScale = imageScaleFactor;
                    float newScale = imageScaleFactor * enhancedFactor;

                    // 最小缩放限制：不能小于默认适配大小（1.0f）
                    newScale = Math.max(1.0f, Math.min(newScale, 5.0f));

                    if (newScale != imageScaleFactor) {
                        // 检查是否达到最小缩放，如果是则触发重置到初始视图
                        if (newScale == 1.0f && imageScaleFactor > 1.0f) {
                            Log.d(TAG, "🖼️ 达到最小缩放，重置到初始视图");
                            resetImageTransform();
                            return true;
                        }

                        float actualFactor = newScale / imageScaleFactor;

                        // 焦点缩放：保持手势中心位置不变
                        float focusX = detector.getFocusX();
                        float focusY = detector.getFocusY();
                        float offsetX = focusX - imageTranslateX;
                        float offsetY = focusY - imageTranslateY;

                        imageScaleFactor = newScale;
                        imageTranslateX = focusX - offsetX * actualFactor;
                        imageTranslateY = focusY - offsetY * actualFactor;

                        updateImageTransformMatrix();
                        applySyncTransform();

                        Log.d(TAG, "🖼️ 焦点缩放: " + oldScale + " -> " + imageScaleFactor +
                                   " (焦点: " + focusX + "," + focusY + ")");
                    }
                    return true;
                }

                // 视频模式：使用原有逻辑
                if (!isInitialized) {
                    Log.w(TAG, "⚠️ Scale attempted but not initialized");
                    return false;
                }

                float scaleFactor = detector.getScaleFactor();
                Log.d(TAG, "🔍 Raw scale factor: " + scaleFactor);

                // 增强缩放敏感度（与TpImageView完全一致）
                float enhancedFactor = 1f + (scaleFactor - 1f) * SCALE_SENSITIVITY;
                float newScale = Math.max(dynamicMinScale, Math.min(currentScale * enhancedFactor, MAX_SCALE));

                if (newScale != currentScale) {
                    // 检查是否达到最小缩放，如果是则触发重置到初始视图
                    if (newScale == dynamicMinScale && currentScale > dynamicMinScale) {
                        Log.d(TAG, "📹 视频达到最小缩放，重置到初始视图");
                        resetAllTransforms();
                        return true;
                    }

                    float actualFactor = newScale / currentScale;
                    currentScale = newScale;
                    float focusX = detector.getFocusX();
                    float focusY = detector.getFocusY();
                    matrix.postScale(actualFactor, actualFactor, focusX, focusY);

                    // 在应用变换前，先同步手势信息到图片
                    syncScaleGestureToPartner(actualFactor, focusX, focusY);

                    updateTextureMatrix(matrix);
                    Log.d(TAG, "✅ Scale applied: " + currentScale + " (factor: " + actualFactor +
                               ", focus: " + focusX + "," + focusY + ")");
                }
                return true;
            }

            @Override
            public void onScaleEnd(ScaleGestureDetector detector) {
                Log.d(TAG, "🔍 Scale end");
                isScaling = false;

                if (isImageMode) {
                    // 图片模式：立即检查边界，防止黑色区域
                    checkImageBounds();
                } else {
                    // 视频模式：同步状态
                    syncCurrentScale();
                }
            }
        });

        // 手势检测器（与TpImageView完全一致）
        gestureDetector = new GestureDetector(getContext(), new GestureDetector.SimpleOnGestureListener() {
            @Override
            public boolean onSingleTapConfirmed(MotionEvent e) {
                Log.d(TAG, "🎯 Single tap confirmed at (" + e.getX() + ", " + e.getY() + ")");
                if (onSingleTapListener != null) {
                    onSingleTapListener.onSingleTap(e);
                }
                return true;
            }

            @Override
            public boolean onScroll(MotionEvent e1, MotionEvent e2, float distanceX, float distanceY) {
                if (isImageMode) {
                    // 图片模式：使用独立的变换系统
                    imageTranslateX -= distanceX;
                    imageTranslateY -= distanceY;

                    updateImageTransformMatrix();
                    applySyncTransform();

                    Log.d(TAG, "🖼️ 图片平移: (" + imageTranslateX + ", " + imageTranslateY + ")");
                    return true;
                }

                // 视频模式：使用原有逻辑
                if (!isInitialized || isScaling) return false;

                // 检查是否在最小缩放状态（与TpImageView一致）
                if (isAtMinimumScale()) {
                    Log.d(TAG, "🚫 Dragging disabled at minimum scale");
                    return false;
                }

                matrix.postTranslate(-distanceX, -distanceY);

                // 同步平移手势到图片
                syncTranslateGestureToPartner(-distanceX, -distanceY);

                updateTextureMatrix(matrix);
                return true;
            }

            @Override
            public boolean onFling(MotionEvent e1, MotionEvent e2, float velocityX, float velocityY) {
                // 移除惯性滑动动画，简化逻辑
                return false;
            }

            @Override
            public boolean onDoubleTap(MotionEvent e) {
                if (isImageMode) {
                    // 图片模式：双击重置变换
                    Log.d(TAG, "🖼️ 双击重置图片变换");
                    resetImageTransform();
                    return true;
                }

                // 视频模式：原有的缩放逻辑
                if (!isInitialized) return false;

                // 获取真实的缩放状态并添加详细调试
                float realScale = getCurrentScaleFactor();
                Log.d(TAG, "🎯 双击前状态: currentScale=" + String.format("%.3f", currentScale) +
                           ", realScale=" + String.format("%.3f", realScale) +
                           ", baseScale=" + String.format("%.3f", baseScale));

                syncCurrentScale(); // 同步状态

                float targetScale = realScale > baseScale * 1.5f ?
                    baseScale : // 缩小到适配大小
                    Math.min(MAX_SCALE, baseScale * 3f); // 放大到3倍

                Log.d(TAG, "🎯 双击目标: " + String.format("%.3f", targetScale) +
                           ", 焦点=(" + String.format("%.1f", e.getX()) + "," + String.format("%.1f", e.getY()) + ")");

                instantScaleTo(targetScale, e.getX(), e.getY());
                return true;
            }

            @Override
            public void onLongPress(MotionEvent e) {
                Log.d(TAG, "🔒 Long press detected - ignoring");
            }
        });
    }

    /**
     * 配置缩放检测器敏感度（与TpImageView一致）
     */
    private void configureScaleDetectorSensitivity() {
        try {
            java.lang.reflect.Field spanSlop = ScaleGestureDetector.class.getDeclaredField("mSpanSlop");
            spanSlop.setAccessible(true);
            spanSlop.setInt(scaleDetector, (int) MIN_SCALE_SPAN);
            Log.d(TAG, "✅ ScaleDetector sensitivity configured: MIN_SCALE_SPAN=" + MIN_SCALE_SPAN);
        } catch (Exception e) {
            Log.w(TAG, "⚠️ Failed to configure ScaleDetector sensitivity", e);
        }
    }

    /**
     * 设置图片模式的手势检测器（完全按照testVieoImage项目）
     */
    private void setupImageGestureDetectors() {
        imageScaleDetector = new ScaleGestureDetector(getContext(), new ScaleGestureDetector.OnScaleGestureListener() {
            @Override
            public boolean onScale(ScaleGestureDetector detector) {
                float scaleFactor = detector.getScaleFactor();
                Log.d(TAG, "🖼️ Raw scale factor: " + scaleFactor);

                // 应用与视频模式相同的敏感度增强
                float enhancedFactor = 1f + (scaleFactor - 1f) * SCALE_SENSITIVITY;
                float oldScale = imageScaleFactor;
                float newScale = imageScaleFactor * enhancedFactor;

                // 图片模式的最小缩放限制：不能小于默认适配大小（1.0f）
                newScale = Math.max(1.0f, Math.min(newScale, 5.0f));

                // 如果缩放值有变化，应用以手势中心为焦点的缩放
                if (newScale != imageScaleFactor) {
                    float actualFactor = newScale / imageScaleFactor;

                    // 获取手势焦点坐标
                    float focusX = detector.getFocusX();
                    float focusY = detector.getFocusY();

                    // 计算焦点相对于当前平移的偏移
                    float offsetX = focusX - imageTranslateX;
                    float offsetY = focusY - imageTranslateY;

                    // 应用缩放
                    imageScaleFactor = newScale;

                    // 调整平移以保持焦点位置不变
                    imageTranslateX = focusX - offsetX * actualFactor;
                    imageTranslateY = focusY - offsetY * actualFactor;

                    updateImageTransformMatrix();
                    applySyncTransform();

                    Log.d(TAG, "🖼️ 焦点缩放: " + oldScale + " -> " + imageScaleFactor +
                               " (焦点: " + focusX + "," + focusY + ", factor: " + actualFactor + ")");
                }
                return true;
            }

            @Override
            public boolean onScaleBegin(ScaleGestureDetector detector) {
                Log.d(TAG, "🖼️ Scale begin - span: " + detector.getCurrentSpan());
                isScaling = true;
                return true;
            }

            @Override
            public void onScaleEnd(ScaleGestureDetector detector) {
                Log.d(TAG, "🖼️ Scale end");
                isScaling = false;
                // 缩放结束后检查边界
                checkImageBounds();
            }
        });

        imageGestureDetector = new GestureDetector(getContext(), new GestureDetector.SimpleOnGestureListener() {
            @Override
            public boolean onScroll(MotionEvent e1, MotionEvent e2, float distanceX, float distanceY) {
                imageTranslateX -= distanceX;
                imageTranslateY -= distanceY;

                updateImageTransformMatrix();
                applySyncTransform();
                return true;
            }

            @Override
            public boolean onDoubleTap(MotionEvent e) {
                // Reset transform on double tap
                resetImageTransform();
                return true;
            }
        });
    }

    /**
     * 设置触摸监听器（与TpImageView完全一致）
     */
    private void setupTouchListener() {
        setOnTouchListener((v, event) -> {
            boolean handled = false;

            if (isZoomEnabled) {
                // 添加调试信息
                if (event.getAction() == MotionEvent.ACTION_DOWN) {
                    Log.d(TAG, "👆 Touch DOWN - 模式: " + (isImageMode ? "图片" : "视频"));
                }

                if (isImageMode) {
                    // 图片模式：添加与视频模式相同的敏感度优化
                    handleCustomScaleForImage(event);

                    // 优先处理缩放手势
                    if (scaleDetector.onTouchEvent(event)) {
                        handled = true;
                    }

                    // 只有在非缩放状态下才处理其他手势
                    if (!scaleDetector.isInProgress() && !isCustomScaling) {
                        if (gestureDetector.onTouchEvent(event)) {
                            handled = true;
                        }
                    }
                } else {
                    // 视频模式：使用原有的复杂手势检测器
                    // 自定义小距离缩放检测（与TpImageView一致）
                    handleCustomScale(event);

                    // 优先处理缩放手势
                    if (scaleDetector.onTouchEvent(event)) {
                        handled = true;
                    }

                    // 只有在非缩放状态下才处理其他手势
                    if (!scaleDetector.isInProgress() && !isCustomScaling) {
                        if (gestureDetector.onTouchEvent(event)) {
                            handled = true;
                        }
                    }
                }

                switch (event.getActionMasked()) {
                    case MotionEvent.ACTION_UP:
                    case MotionEvent.ACTION_CANCEL:
                        if (!isScaling && !isCustomScaling) {
                            if (isImageMode) {
                                // 图片模式：检查图片边界
                                checkImageBounds();
                            } else {
                                // 视频模式：检查视频边界
                                instantCheckBounds();
                            }
                        }
                        // 触摸结束时同步状态
                        syncCurrentScale();
                        isCustomScaling = false;
                        lastDistance = -1f;
                        break;
                }
            }

            // 重要：如果事件被处理了，就不要调用 performClick()
            if (!handled) {
                performClick();
            }

            // 总是返回 true 来消费事件，避免传递给父视图
            return true;
        });
    }

    /**
     * 自定义缩放检测（与TpImageView完全一致）
     */
    private void handleCustomScale(MotionEvent event) {
        if (event.getPointerCount() != 2) {
            lastDistance = -1f;
            return;
        }

        float distance = getDistance(event);

        if (lastDistance > 0) {
            float deltaDistance = Math.abs(distance - lastDistance);

            if (deltaDistance > CUSTOM_SCALE_THRESHOLD) {
                isCustomScaling = true;

                float scaleFactor = distance / lastDistance;
                float enhancedFactor = 1f + (scaleFactor - 1f) * SCALE_SENSITIVITY;
                float newScale = Math.max(dynamicMinScale, Math.min(currentScale * enhancedFactor, MAX_SCALE));

                if (newScale != currentScale) {
                    // 检查是否达到最小缩放，如果是则触发重置到初始视图
                    if (newScale == dynamicMinScale && currentScale > dynamicMinScale) {
                        Log.d(TAG, "📹 自定义缩放达到最小值，重置到初始视图");
                        resetAllTransforms();
                        return;
                    }

                    float actualFactor = newScale / currentScale;
                    currentScale = newScale;

                    float focusX = (event.getX(0) + event.getX(1)) / 2f;
                    float focusY = (event.getY(0) + event.getY(1)) / 2f;

                    matrix.postScale(actualFactor, actualFactor, focusX, focusY);
                    updateTextureMatrix(matrix);

                    Log.d(TAG, "🎯 Custom scale: " + currentScale + " (delta: " + deltaDistance + ")");
                }
            }
        }

        lastDistance = distance;
        lastFocusX = (event.getX(0) + event.getX(1)) / 2f;
        lastFocusY = (event.getY(0) + event.getY(1)) / 2f;
    }

    /**
     * 计算两点间距离
     */
    private float getDistance(MotionEvent event) {
        if (event.getPointerCount() < 2) return 0f;

        float dx = event.getX(0) - event.getX(1);
        float dy = event.getY(0) - event.getY(1);
        return (float) Math.sqrt(dx * dx + dy * dy);
    }

    /**
     * 获取当前真实缩放因子（修正为基于baseScale的计算）
     */
    private float getCurrentScaleFactor() {
        float[] values = new float[9];
        matrix.getValues(values);
        float scaleX = values[Matrix.MSCALE_X];
        float scaleY = values[Matrix.MSCALE_Y];

        // 计算相对于基础缩放的倍数
        float relativeScaleX = scaleX / baseScaleX;
        float relativeScaleY = scaleY / baseScaleY;

        // 取平均值作为统一的缩放因子
        float avgScale = (relativeScaleX + relativeScaleY) / 2f;

        return avgScale * baseScale;
    }

    /**
     * 同步当前缩放状态（修正状态同步逻辑）
     */
    private void syncCurrentScale() {
        if (!isInitialized) return;

        float realScale = getCurrentScaleFactor();
        if (Math.abs(realScale - currentScale) > 0.01f) {
            Log.d(TAG, "🔄 Syncing scale: " + currentScale + " -> " + realScale);
            currentScale = realScale;
        }
    }

    /**
     * 检查是否处于最小缩放状态（与TpImageView完全一致）
     */
    private boolean isAtMinimumScale() {
        float realScale = getCurrentScaleFactor();
        return realScale <= dynamicMinScale * 1.01f;
    }

    /**
     * 边界检查和自动调整（移除动画，直接修正）
     */
    private void instantCheckBounds() {
        if (!isInitialized) return;

        RectF rect = getDisplayRect();
        float deltaX = 0f;
        float deltaY = 0f;

        Log.d(TAG, "🔍 边界检查: 显示区域=" + String.format("%.1f,%.1f-%.1f,%.1f",
               rect.left, rect.top, rect.right, rect.bottom));

        // X轴边界检查
        if (rect.width() <= viewWidth) {
            // 内容宽度小于等于视图宽度，居中显示
            deltaX = (viewWidth - rect.width()) / 2f - rect.left;
        } else if (rect.left > 0) {
            // 内容左边界超出视图左边界
            deltaX = -rect.left;
        } else if (rect.right < viewWidth) {
            // 内容右边界超出视图右边界
            deltaX = viewWidth - rect.right;
        }

        // Y轴边界检查
        if (rect.height() <= viewHeight) {
            // 内容高度小于等于视图高度，居中显示
            deltaY = (viewHeight - rect.height()) / 2f - rect.top;
        } else if (rect.top > 0) {
            // 内容上边界超出视图上边界
            deltaY = -rect.top;
        } else if (rect.bottom < viewHeight) {
            // 内容下边界超出视图下边界
            deltaY = viewHeight - rect.bottom;
        }

        // 🔧 关键修正：直接应用位置修正，移除动画
        if (Math.abs(deltaX) > 1f || Math.abs(deltaY) > 1f) {
            Log.d(TAG, "🎯 直接修正位置: deltaX=" + String.format("%.1f", deltaX) +
                       ", deltaY=" + String.format("%.1f", deltaY));

            matrix.postTranslate(deltaX, deltaY);
            updateTextureMatrix(matrix);
            syncCurrentScale();
        } else {
            Log.d(TAG, "✅ 边界检查完成，无需修正");
            syncCurrentScale();
        }
    }





    /**
     * 瞬间缩放到指定比例（修正时序问题）
     */
    private void instantScaleTo(float targetScale, float focusX, float focusY) {
        if (!isInitialized) return;

        Log.d(TAG, "🎯 开始瞬间缩放: 从" + String.format("%.3f", currentScale) +
                   "到" + String.format("%.3f", targetScale));

        float scaleFactor = targetScale / currentScale;
        currentScale = targetScale;

        // 应用缩放变换
        matrix.postScale(scaleFactor, scaleFactor, focusX, focusY);
        updateTextureMatrix(matrix);

        // 🔧 关键修正：先同步状态，再延迟执行边界检查
        syncCurrentScale();
        post(() -> {
            Log.d(TAG, "🔍 延迟执行边界检查");
            instantCheckBounds();
        });

        Log.d(TAG, "✅ 瞬间缩放完成，边界检查已延迟执行");
    }

    /**
     * 获取显示区域（修正为基于TextureView的逻辑）
     */
    private RectF getDisplayRect() {
        // TextureView的显示区域是基于View尺寸，而不是视频尺寸
        RectF rect = new RectF(0f, 0f, viewWidth, viewHeight);
        matrix.mapRect(rect);
        return rect;
    }



    // ========== 公共API方法 ==========

    /**
     * 获取当前缩放比例
     */
    public float getCurrentScale() {
        return currentScale;
    }

    /**
     * 获取基础缩放比例
     */
    public float getBaseScale() {
        return baseScale;
    }

    /**
     * 设置是否启用缩放
     */
    public void setZoomEnabled(boolean enabled) {
        this.isZoomEnabled = enabled;
        Log.d(TAG, "Zoom enabled: " + enabled);
    }

    /**
     * 是否启用缩放
     */
    public boolean isZoomEnabled() {
        return isZoomEnabled;
    }

    /**
     * 设置单击监听器
     */
    public void setOnSingleTapListener(OnSingleTapListener listener) {
        this.onSingleTapListener = listener;
    }

    /**
     * 设置Matrix变化监听器
     */
    public void setOnMatrixChangeListener(OnMatrixChangeListener listener) {
        this.matrixChangeListener = listener;
    }

    /**
     * 设置伙伴视图，用于Matrix同步
     * @param partner 伙伴TpTextureView
     */
    public void setPartnerView(TpTextureView partner) {
        this.partnerView = partner;
        Log.d(TAG, "设置伙伴视图: " + (partner != null ? "已设置" : "已清除"));
    }

    /**
     * 应用外部Matrix（用于同步）
     * @param externalMatrix 外部Matrix
     */
    public void applyExternalMatrix(Matrix externalMatrix) {
        if (externalMatrix == null || isSyncing) return;

        if (isImageMode) {
            // 图片模式：从外部Matrix提取变换参数（不触发视频变换逻辑）
            float[] values = new float[9];
            externalMatrix.getValues(values);

            // 提取缩放和平移参数
            float scaleX = values[Matrix.MSCALE_X];
            float scaleY = values[Matrix.MSCALE_Y];
            float translateX = values[Matrix.MTRANS_X];
            float translateY = values[Matrix.MTRANS_Y];

            // 假设X和Y缩放相同（通常情况下）
            imageScaleFactor = scaleX;
            imageTranslateX = translateX;
            imageTranslateY = translateY;

            // 只更新图片变换，不触发任何视频逻辑
            updateImageTransformMatrix();
            drawImageOnCanvas();

            Log.d(TAG, "🖼️ 应用外部Matrix到图片: scale=" + imageScaleFactor +
                       ", translate=(" + imageTranslateX + ", " + imageTranslateY + ")");
        } else {
            // 视频模式：使用原有逻辑
            Log.d(TAG, "📹 应用外部Matrix到视频");

            // 复制Matrix并应用
            matrix.set(externalMatrix);

            // 同步当前缩放状态
            syncCurrentScale();

            // 更新显示（不触发同步，避免循环）
            setTransform(matrix);
            invalidate();
            refreshImageDisplay();
        }
    }

    /**
     * 重置到初始状态
     */
    public void resetToInitialState() {
        if (!isInitialized) return;

        currentScale = baseScale;
        matrix.set(baseMatrix);
        updateTextureMatrix(matrix);

        Log.d(TAG, "Reset to initial state");
    }

    /**
     * 获取当前Matrix的副本
     */
    public Matrix getCurrentMatrix() {
        return new Matrix(matrix);
    }

    /**
     * 加载图片到TextureView
     * @param imagePath 图片路径
     */
    public void loadImage(String imagePath) {
        if (imagePath == null || imagePath.isEmpty()) {
            Log.e(TAG, "图片路径为空");
            return;
        }

        this.currentImagePath = imagePath;
        this.isImageMode = true;

        Log.d(TAG, "开始加载图片: " + imagePath);

        // 异步加载图片
        new Thread(() -> {
            try {
                // 使用BitmapFactory直接加载图片
                android.graphics.BitmapFactory.Options options = new android.graphics.BitmapFactory.Options();
                options.inJustDecodeBounds = true;
                android.graphics.BitmapFactory.decodeFile(imagePath, options);

                // 计算合适的采样率
                options.inSampleSize = calculateInSampleSize(options, 2048, 2048);
                options.inJustDecodeBounds = false;

                android.graphics.Bitmap bitmap = android.graphics.BitmapFactory.decodeFile(imagePath, options);

                if (bitmap != null) {
                    post(() -> {
                        currentBitmap = bitmap;

                        // 图片模式：简单初始化（完全按照testVieoImage项目）
                        imageScaleFactor = 1.0f;
                        imageTranslateX = 0f;
                        imageTranslateY = 0f;
                        updateImageTransformMatrix();

                        // 立即绘制图片
                        drawImageOnCanvas();

                        Log.d(TAG, "🖼️ 图片加载完成: " + bitmap.getWidth() + "x" + bitmap.getHeight());
                    });
                } else {
                    post(() -> Log.e(TAG, "图片加载失败: " + imagePath));
                }
            } catch (Exception e) {
                post(() -> Log.e(TAG, "加载图片时发生错误: " + imagePath, e));
            }
        }).start();
    }

    /**
     * 计算图片采样率
     */
    private int calculateInSampleSize(android.graphics.BitmapFactory.Options options, int reqWidth, int reqHeight) {
        final int height = options.outHeight;
        final int width = options.outWidth;
        int inSampleSize = 1;

        if (height > reqHeight || width > reqWidth) {
            final int halfHeight = height / 2;
            final int halfWidth = width / 2;

            while ((halfHeight / inSampleSize) >= reqHeight && (halfWidth / inSampleSize) >= reqWidth) {
                inSampleSize *= 2;
            }
        }

        return inSampleSize;
    }

    /**
     * 在Canvas上绘制图片（完全按照testVieoImage项目的逻辑）
     */
    private void drawImageOnCanvas() {
        if (!isImageMode || currentBitmap == null || currentBitmap.isRecycled()) {
            return;
        }

        Canvas canvas = lockCanvas();
        if (canvas != null) {
            try {
                // 清除画布
                canvas.drawColor(android.graphics.Color.BLACK);

                // 计算图片适配到TextureView的缩放比例（FIT_CENTER逻辑）
                float viewWidth = getWidth();
                float viewHeight = getHeight();
                float bitmapWidth = currentBitmap.getWidth();
                float bitmapHeight = currentBitmap.getHeight();

                if (viewWidth > 0 && viewHeight > 0 && bitmapWidth > 0 && bitmapHeight > 0) {
                    // 计算适配缩放比例（保持图片比例，完整显示）
                    float scaleX = viewWidth / bitmapWidth;
                    float scaleY = viewHeight / bitmapHeight;
                    float scale = Math.min(scaleX, scaleY);

                    // 尝试不同的Matrix变换顺序来修复居中问题
                    Matrix matrix = new Matrix();

                    // 第一步：先应用适配缩放和居中
                    matrix.postScale(scale, scale);
                    matrix.postTranslate((viewWidth - bitmapWidth * scale) / 2, (viewHeight - bitmapHeight * scale) / 2);

                    // 第二步：再应用用户变换
                    matrix.postConcat(imageTransformMatrix);

                    // 绘制图片
                    canvas.drawBitmap(currentBitmap, matrix, null);

                    // 详细的调试信息
                    float centerX = (viewWidth - bitmapWidth * scale) / 2;
                    float centerY = (viewHeight - bitmapHeight * scale) / 2;
                    float scaledWidth = bitmapWidth * scale;
                    float scaledHeight = bitmapHeight * scale;

                    Log.d(TAG, "🖼️ 图片绘制详情:");
                    Log.d(TAG, "  容器尺寸: " + viewWidth + "x" + viewHeight);
                    Log.d(TAG, "  原图尺寸: " + bitmapWidth + "x" + bitmapHeight);
                    Log.d(TAG, "  适配缩放: " + scale);
                    Log.d(TAG, "  缩放后尺寸: " + scaledWidth + "x" + scaledHeight);
                    Log.d(TAG, "  居中偏移: (" + centerX + ", " + centerY + ")");
                    Log.d(TAG, "  用户变换: scale=" + imageScaleFactor + ", translate=(" + imageTranslateX + ", " + imageTranslateY + ")");
                } else {
                    Log.w(TAG, "无效的尺寸参数，跳过图片绘制");
                }
            } catch (Exception e) {
                Log.e(TAG, "绘制图片时出错", e);
            } finally {
                unlockCanvasAndPost(canvas);
            }
        } else {
            Log.w(TAG, "无法获取Canvas，跳过图片绘制");
        }
    }

    /**
     * 刷新图片显示（在Matrix变化时调用）
     */
    private void refreshImageDisplay() {
        if (isImageMode && currentBitmap != null) {
            post(() -> drawImageOnCanvas());
        }
    }

    @Override
    public boolean performClick() {
        return super.performClick();
    }

    /**
     * 清理图片资源
     */
    public void clearImage() {
        if (currentBitmap != null && !currentBitmap.isRecycled()) {
            currentBitmap.recycle();
            currentBitmap = null;
        }
        isImageMode = false;
        currentImagePath = null;
        Log.d(TAG, "图片资源已清理");
    }

    /**
     * 是否处于图片模式
     */
    public boolean isImageMode() {
        return isImageMode;
    }

    /**
     * 获取当前图片路径
     */
    public String getCurrentImagePath() {
        return currentImagePath;
    }

    /**
     * 获取伙伴视图
     */
    public TpTextureView getPartnerView() {
        return partnerView;
    }

    /**
     * 图片模式的自定义小距离缩放检测（与视频模式保持一致）
     */
    private void handleCustomScaleForImage(MotionEvent event) {
        if (event.getPointerCount() == 2) {
            float x1 = event.getX(0);
            float y1 = event.getY(0);
            float x2 = event.getX(1);
            float y2 = event.getY(1);
            float distance = (float) Math.sqrt((x2 - x1) * (x2 - x1) + (y2 - y1) * (y2 - y1));

            if (lastDistance > 0) {
                float deltaDistance = Math.abs(distance - lastDistance);

                if (deltaDistance > CUSTOM_SCALE_THRESHOLD) {
                    isCustomScaling = true;

                    float scaleFactor = distance / lastDistance;
                    float enhancedFactor = 1f + (scaleFactor - 1f) * SCALE_SENSITIVITY;
                    float oldScale = imageScaleFactor;
                    float newScale = imageScaleFactor * enhancedFactor;

                    // 图片模式的最小缩放限制：不能小于默认适配大小（1.0f）
                    newScale = Math.max(1.0f, Math.min(newScale, 5.0f));

                    // 如果缩放值有变化，应用以手势中心为焦点的缩放
                    if (newScale != imageScaleFactor) {
                        // 检查是否达到最小缩放，如果是则触发重置到初始视图
                        if (newScale == 1.0f && imageScaleFactor > 1.0f) {
                            Log.d(TAG, "🖼️ 自定义缩放达到最小值，重置到初始视图");
                            resetImageTransform();
                            return;
                        }

                        float actualFactor = newScale / imageScaleFactor;

                        // 计算两指中心点作为焦点
                        float focusX = (x1 + x2) / 2;
                        float focusY = (y1 + y2) / 2;

                        // 计算焦点相对于当前平移的偏移
                        float offsetX = focusX - imageTranslateX;
                        float offsetY = focusY - imageTranslateY;

                        // 应用缩放
                        imageScaleFactor = newScale;

                        // 调整平移以保持焦点位置不变
                        imageTranslateX = focusX - offsetX * actualFactor;
                        imageTranslateY = focusY - offsetY * actualFactor;

                        updateImageTransformMatrix();
                        applySyncTransform();

                        Log.d(TAG, "🖼️ 自定义焦点缩放: " + oldScale + " -> " + imageScaleFactor +
                                   " (焦点: " + focusX + "," + focusY + ", factor: " + actualFactor + ")");
                    }
                }
            }
            lastDistance = distance;
        } else {
            lastDistance = -1f;
        }
    }

    /**
     * 更新图片变换矩阵（类似testVieoImage项目的updateTransformMatrix）
     */
    private void updateImageTransformMatrix() {
        imageTransformMatrix.reset();
        imageTransformMatrix.postScale(imageScaleFactor, imageScaleFactor);
        imageTransformMatrix.postTranslate(imageTranslateX, imageTranslateY);
    }

    /**
     * 应用同步变换（修复视频变换问题）
     */
    private void applySyncTransform() {
        if (!isSyncing && partnerView != null) {
            isSyncing = true;
            try {
                if (partnerView.isImageMode) {
                    // 伙伴视图是图片模式，同步图片变换参数
                    partnerView.imageScaleFactor = this.imageScaleFactor;
                    partnerView.imageTranslateX = this.imageTranslateX;
                    partnerView.imageTranslateY = this.imageTranslateY;
                    partnerView.updateImageTransformMatrix();
                    partnerView.drawImageOnCanvas();
                } else {
                    // 伙伴视图是视频模式，需要将图片变换合并到视频的baseMatrix上
                    // 而不是直接覆盖，以保持视频的FIT_CENTER逻辑
                    Matrix combinedMatrix = new Matrix(partnerView.baseMatrix);
                    combinedMatrix.postConcat(imageTransformMatrix);
                    partnerView.matrix.set(combinedMatrix);
                    partnerView.setTransform(combinedMatrix);

                    Log.d(TAG, "🖼️ 图片变换已合并到视频baseMatrix");
                }

                // 重绘当前图片
                drawImageOnCanvas();

                Log.d(TAG, "🖼️ 同步变换完成: scale=" + imageScaleFactor +
                           ", translate=(" + imageTranslateX + ", " + imageTranslateY + ")");
            } finally {
                isSyncing = false;
            }
        }
    }



    /**
     * 重置图片变换（完全按照testVieoImage项目的resetTransform）
     */
    public void resetImageTransform() {
        if (isImageMode) {
            imageScaleFactor = 1.0f;
            imageTranslateX = 0f;
            imageTranslateY = 0f;
            updateImageTransformMatrix();
            applySyncTransform();
            Log.d(TAG, "🖼️ 图片变换已重置: scale=" + imageScaleFactor +
                       ", translate=(" + imageTranslateX + ", " + imageTranslateY + ")");
        }
    }

    /**
     * 重置所有变换（图片或视频）
     */
    public void resetAllTransforms() {
        if (isImageMode) {
            resetImageTransform();
        } else {
            // 重置视频变换
            currentScale = baseScale;
            matrix.set(baseMatrix);
            updateTextureMatrix(matrix);
            Log.d(TAG, "📹 视频变换已重置");
        }
    }

    /**
     * 检查图片是否处于最小缩放状态
     */
    private boolean isImageAtMinimumScale() {
        return isImageMode && Math.abs(imageScaleFactor - 1.0f) < 0.01f;
    }

    /**
     * 图片模式的边界检查和自动调整（重写以修复坐标系问题）
     */
    private void checkImageBounds() {
        if (!isImageMode || currentBitmap == null || currentBitmap.isRecycled()) return;

        float viewWidth = getWidth();
        float viewHeight = getHeight();
        float bitmapWidth = currentBitmap.getWidth();
        float bitmapHeight = currentBitmap.getHeight();

        if (viewWidth <= 0 || viewHeight <= 0 || bitmapWidth <= 0 || bitmapHeight <= 0) return;

        // 使用与绘制时完全相同的Matrix变换来计算显示区域
        float scaleX = viewWidth / bitmapWidth;
        float scaleY = viewHeight / bitmapHeight;
        float baseScale = Math.min(scaleX, scaleY);

        // 创建与绘制时相同的变换矩阵
        Matrix drawMatrix = new Matrix();

        // 第一步：应用适配缩放和居中（与drawImageOnCanvas中的逻辑一致）
        drawMatrix.postScale(baseScale, baseScale);
        drawMatrix.postTranslate((viewWidth - bitmapWidth * baseScale) / 2, (viewHeight - bitmapHeight * baseScale) / 2);

        // 第二步：应用用户变换
        drawMatrix.postConcat(imageTransformMatrix);

        // 计算图片在变换后的显示区域
        RectF imageRect = new RectF(0, 0, bitmapWidth, bitmapHeight);
        drawMatrix.mapRect(imageRect);

        float deltaX = 0f;
        float deltaY = 0f;

        Log.d(TAG, "🖼️ 图片边界检查: 显示区域=" + String.format("%.1f,%.1f-%.1f,%.1f",
               imageRect.left, imageRect.top, imageRect.right, imageRect.bottom) +
               ", 容器=" + viewWidth + "x" + viewHeight);

        // X轴边界检查（与视频模式逻辑完全一致）
        if (imageRect.width() <= viewWidth) {
            // 图片宽度小于等于视图宽度，居中显示
            deltaX = (viewWidth - imageRect.width()) / 2f - imageRect.left;
        } else if (imageRect.left > 0) {
            // 图片左边界超出视图左边界
            deltaX = -imageRect.left;
        } else if (imageRect.right < viewWidth) {
            // 图片右边界超出视图右边界
            deltaX = viewWidth - imageRect.right;
        }

        // Y轴边界检查（与视频模式逻辑完全一致）
        if (imageRect.height() <= viewHeight) {
            // 图片高度小于等于视图高度，居中显示
            deltaY = (viewHeight - imageRect.height()) / 2f - imageRect.top;
        } else if (imageRect.top > 0) {
            // 图片上边界超出视图上边界
            deltaY = -imageRect.top;
        } else if (imageRect.bottom < viewHeight) {
            // 图片下边界超出视图下边界
            deltaY = viewHeight - imageRect.bottom;
        }

        // 应用位置修正（只有在真正需要时才修正）
        if (Math.abs(deltaX) > 1f || Math.abs(deltaY) > 1f) {
            Log.d(TAG, "🖼️ 修正图片位置: deltaX=" + String.format("%.1f", deltaX) +
                       ", deltaY=" + String.format("%.1f", deltaY));

            // 临时禁用同步，防止边界检查触发循环同步
            boolean wasSyncing = isSyncing;
            isSyncing = true;

            imageTranslateX += deltaX;
            imageTranslateY += deltaY;
            updateImageTransformMatrix();
            drawImageOnCanvas(); // 直接绘制，不触发同步

            // 恢复同步状态
            isSyncing = wasSyncing;
        }
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        clearImage();
        partnerView = null;
        Log.d(TAG, "TpTextureView detached from window");
    }
}
