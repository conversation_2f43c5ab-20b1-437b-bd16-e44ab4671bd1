package com.touptek.ui.compare

import android.graphics.Matrix
import android.util.Log
import com.touptek.ui.TpImageView

/**
 * 图片Matrix同步引擎
 *
 * 负责管理多个TpImageView之间的Matrix同步逻辑
 */
open class TpImageSyncEngine {

    companion object {
        private const val TAG = "TpImageSyncEngine"

        @JvmStatic
        fun create(): TpImageSyncEngine = TpImageSyncEngine()
    }
    
    private val imageViews = mutableMapOf<String, TpImageView>()
    private val matrixListeners = mutableMapOf<String, () -> Unit>()
    private var syncMode = SyncMode.GLOBAL
    private var isSyncEnabled = true
    private var isUpdating = false

    enum class SyncMode {
        GLOBAL,     // 全局同步 - 所有图片完全同步
        GROUP,      // 分组同步 - 按组同步
        INDEPENDENT // 独立模式 - 每张图片独立操作
    }
    
    /**
     * 添加需要同步的ImageView
     */
    fun addImageView(id: String, imageView: TpImageView) {
        imageViews[id] = imageView
        
        // 设置Matrix变化监听器
        val listener = {
            if (isSyncEnabled && !isUpdating) {
                syncMatrixFromSource(id)
            }
        }
        matrixListeners[id] = listener
        imageView.setMatrixChangeListener(listener)
        
        Log.d(TAG, "添加ImageView: $id")
    }
    

    
    /**
     * 设置同步模式
     */
    fun setSyncMode(mode: SyncMode) {
        this.syncMode = mode
        Log.d(TAG, "同步模式设置为: $mode")
    }
    
    /**
     * 启用/禁用同步
     */
    fun setSyncEnabled(enabled: Boolean) {
        this.isSyncEnabled = enabled
        Log.d(TAG, "同步${if (enabled) "启用" else "禁用"}")
    }
    
    /**
     * 从源ImageView同步Matrix到其他ImageView
     */
    private fun syncMatrixFromSource(sourceId: String) {
        isUpdating = true
        try {
            val sourceMatrix = imageViews[sourceId]?.imageMatrix ?: return
            val targetIds = getTargetIds(sourceId)
            
            targetIds.forEach { targetId ->
                imageViews[targetId]?.imageMatrix = Matrix(sourceMatrix)
            }
            
            Log.d(TAG, "从 $sourceId 同步Matrix到 ${targetIds.size} 个目标")
        } catch (e: Exception) {
            Log.e(TAG, "同步Matrix失败", e)
        } finally {
            isUpdating = false
        }
    }
    
    /**
     * 根据同步模式获取目标ImageView ID列表
     */
    private fun getTargetIds(sourceId: String): List<String> {
        return when (syncMode) {
            SyncMode.GLOBAL -> {
                // 全局同步：同步到所有其他ImageView
                imageViews.keys.filter { it != sourceId }
            }
            SyncMode.GROUP -> {
                // 分组同步：根据具体业务逻辑实现
                getGroupTargets(sourceId)
            }
            SyncMode.INDEPENDENT -> {
                // 独立模式：不同步
                emptyList()
            }
        }
    }
    
    /**
     * 获取分组同步的目标列表
     * 可以根据具体需求重写此方法
     */
    protected open fun getGroupTargets(sourceId: String): List<String> {
        // 默认实现：简单的分组逻辑
        // 可以在具体的Activity中重写此逻辑
        return when {
            sourceId.contains("left") || sourceId.contains("center") -> {
                imageViews.keys.filter { 
                    it != sourceId && (it.contains("left") || it.contains("center"))
                }
            }
            else -> emptyList()
        }
    }
    

}
