package com.touptek.xcamview.activity.ispdialogfragment
import android.app.AlertDialog
import android.os.Bundle
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.WindowManager
import android.widget.AdapterView
import android.widget.ArrayAdapter
import android.widget.Button
import android.widget.EditText
import android.widget.Spinner
import android.widget.Toast
import androidx.fragment.app.DialogFragment
import com.touptek.xcamview.R
import com.touptek.xcamview.util.BaseDialogFragment
import com.touptek.video.TpIspParam

class TpSceneDialogFragment : BaseISPDialogFragment() {

    private lateinit var sceneSpinner: Spinner
    private lateinit var addButton: Button
    private lateinit var deleteButton: Button
    private lateinit var sceneAdapter: ArrayAdapter<String>
    private var sceneList = mutableListOf<String>()

    // 标志位：用于区分是初始化触发还是用户主动选择
    private var isInitializing = true

    companion object {
        private const val TAG = "TpSceneDialogFragment"
        private val SYSTEM_SCENES = listOf("生物", "体视")
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        val view = inflater.inflate(R.layout.scene_layout, container, false)

        initViews(view)
        setupSpinner()
        setupButtons()

        return view
    }

    private fun initViews(view: View) {
        sceneSpinner = view.findViewById(R.id.scene_spinner)
        addButton = view.findViewById(R.id.btn_add_scene)
        deleteButton = view.findViewById(R.id.btn_delete_scene)
    }

    private fun setupSpinner() {
        // 标记开始初始化
        isInitializing = true

        // 获取场景列表
        refreshSceneList()

        // 设置适配器
        sceneAdapter = ArrayAdapter(requireContext(), android.R.layout.simple_spinner_item, sceneList)
        sceneAdapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        sceneSpinner.adapter = sceneAdapter

        // 设置当前场景选中状态（不触发应用）
        setCurrentSceneSelection()

        // 设置选择监听器
        sceneSpinner.onItemSelectedListener = object : AdapterView.OnItemSelectedListener {
            override fun onItemSelected(parent: AdapterView<*>?, view: View?, position: Int, id: Long) {
                val selectedScene = sceneList[position]
                Log.d(TAG, "场景选择事件: $selectedScene, 是否初始化中: $isInitializing")

                // 只有在非初始化状态下才应用场景
                if (!isInitializing) {
                    Log.d(TAG, "用户主动选择场景，应用场景: $selectedScene")
                    applyScene(selectedScene)
                } else {
                    Log.d(TAG, "初始化中，跳过场景应用")
                }

                // 更新删除按钮状态
                updateDeleteButtonState(selectedScene)
            }

            override fun onNothingSelected(parent: AdapterView<*>?) {}
        }

        // 初始化完成
        isInitializing = false
        Log.d(TAG, "场景Spinner初始化完成")
    }

    private fun setupButtons() {
        // 添加场景按钮
        addButton.setOnClickListener {
            showAddSceneDialog()
        }

        // 删除场景按钮
        deleteButton.setOnClickListener {
            showDeleteSceneDialog()
        }
    }

    private fun showAddSceneDialog() {
        val editText = EditText(requireContext()).apply {
            hint = "请输入场景名称"
            setPadding(50, 30, 50, 30)
        }

        AlertDialog.Builder(requireContext())
            .setTitle("添加新场景")
            .setMessage("输入场景名称，将保存当前设置为新场景")
            .setView(editText)
            .setPositiveButton("确认") { _, _ ->
                val sceneName = editText.text.toString().trim()
                if (sceneName.isNotEmpty()) {
                    addNewScene(sceneName)
                } else {
                    Toast.makeText(requireContext(), "场景名称不能为空", Toast.LENGTH_SHORT).show()
                }
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun showDeleteSceneDialog() {
        val selectedScene = getCurrentSelectedScene()
        if (selectedScene.isEmpty()) {
            Toast.makeText(requireContext(), "请先选择要删除的场景", Toast.LENGTH_SHORT).show()
            return
        }

        AlertDialog.Builder(requireContext())
            .setTitle("删除场景")
            .setMessage("删除该场景后，将自动切换至生物场景，是否确认删除？")
            .setPositiveButton("确认") { _, _ ->
                deleteScene(selectedScene)
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun addNewScene(sceneName: String) {
        try {
            // 检查场景名是否已存在
            if (sceneList.contains(sceneName)) {
                Toast.makeText(requireContext(), "场景名称已存在", Toast.LENGTH_SHORT).show()
                return
            }

            // 保存当前设置为新场景
            TpIspParam.saveCurrentAsScene(sceneName)
            Log.d(TAG, "保存新场景: $sceneName")

            // 刷新场景列表
            refreshSceneList()
            sceneAdapter.notifyDataSetChanged()

            // 设置为当前选中的场景（这里是用户主动操作，允许触发应用）
            val position = sceneList.indexOf(sceneName)
            if (position >= 0) {
                // 这里不需要设置isInitializing，因为这是用户主动添加的场景
                // 应该允许应用新创建的场景
                sceneSpinner.setSelection(position)
                Log.d(TAG, "新场景已选中并应用: $sceneName")
            }

            Toast.makeText(requireContext(), "场景 '$sceneName' 添加成功", Toast.LENGTH_SHORT).show()

        } catch (e: Exception) {
            Log.e(TAG, "添加场景失败", e)
            Toast.makeText(requireContext(), "添加场景失败: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }

    private fun deleteScene(sceneName: String) {
        try {
            // 删除场景
            TpIspParam.deleteScene(sceneName)
            Log.d(TAG, "删除场景: $sceneName")

            // 刷新场景列表
            refreshSceneList()
            sceneAdapter.notifyDataSetChanged()

            // 自动切换到生物场景（这里是系统自动操作，需要手动应用场景）
            val biologicalPosition = sceneList.indexOf("生物")
            if (biologicalPosition >= 0) {
                // 设置选中状态，但不触发监听器
                sceneSpinner.setSelection(biologicalPosition, false)
                // 手动应用生物场景
                applyScene("生物")
                Log.d(TAG, "删除场景后自动切换到生物场景")
            }

            Toast.makeText(requireContext(), "场景 '$sceneName' 删除成功", Toast.LENGTH_SHORT).show()

        } catch (e: Exception) {
            Log.e(TAG, "删除场景失败", e)
            Toast.makeText(requireContext(), "删除场景失败: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }

    private fun applyScene(sceneName: String) {
        try {
            TpIspParam.applyScene(sceneName)
            Log.d(TAG, "应用场景: $sceneName")
        } catch (e: Exception) {
            Log.e(TAG, "应用场景失败", e)
            Toast.makeText(requireContext(), "应用场景失败: ${e.message}", Toast.LENGTH_SHORT).show()
        }
    }

    private fun refreshSceneList() {
        sceneList.clear()
        sceneList.addAll(TpIspParam.getAllSceneNames())
        Log.d(TAG, "刷新场景列表: $sceneList")
    }

    /**
     * 设置当前场景的选中状态（不触发应用场景）
     */
    private fun setCurrentSceneSelection() {
        try {
            // 尝试获取当前场景名称
            val currentScene = getCurrentSceneFromSystem()

            if (currentScene.isNotEmpty()) {
                val position = sceneList.indexOf(currentScene)
                if (position >= 0) {
                    sceneSpinner.setSelection(position, false) // false表示不触发监听器
                    Log.d(TAG, "设置当前场景选中: $currentScene (位置: $position)")
                } else {
                    Log.w(TAG, "当前场景 '$currentScene' 不在场景列表中")
                    // 如果当前场景不在列表中，默认选择第一个场景
                    if (sceneList.isNotEmpty()) {
                        sceneSpinner.setSelection(0, false)
                        Log.d(TAG, "默认选择第一个场景: ${sceneList[0]}")
                    }
                }
            } else {
                // 如果无法获取当前场景，默认选择第一个场景
                if (sceneList.isNotEmpty()) {
                    sceneSpinner.setSelection(0, false)
                    Log.d(TAG, "无法获取当前场景，默认选择第一个场景: ${sceneList[0]}")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "设置当前场景选中状态失败", e)
            // 异常情况下，默认选择第一个场景
            if (sceneList.isNotEmpty()) {
                sceneSpinner.setSelection(0, false)
            }
        }
    }

    /**
     * 从系统获取当前场景名称
     * 注意：这里假设TpIspParam有获取当前场景的方法，如果没有则需要其他方式
     */
    private fun getCurrentSceneFromSystem(): String {
        return try {
            // 尝试调用TpIspParam的getCurrentScene方法
            // 如果该方法不存在，可以使用其他方式获取当前场景
            TpIspParam.getCurrentSceneName() ?: ""
        } catch (e: Exception) {
            Log.w(TAG, "无法从系统获取当前场景，可能该方法不存在", e)
            // 如果没有getCurrentScene方法，可以返回默认场景或空字符串
            // 这种情况下会默认选择列表中的第一个场景
            ""
        }
    }

    private fun getCurrentSelectedScene(): String {
        val position = sceneSpinner.selectedItemPosition
        return if (position >= 0 && position < sceneList.size) {
            sceneList[position]
        } else {
            ""
        }
    }

    private fun updateDeleteButtonState(sceneName: String) {
        // 系统预设场景（生物、体视）不允许删除
        val isSystemScene = SYSTEM_SCENES.contains(sceneName)
        deleteButton.isEnabled = !isSystemScene

        if (isSystemScene) {
            deleteButton.alpha = 0.5f
        } else {
            deleteButton.alpha = 1.0f
        }

        Log.d(TAG, "更新删除按钮状态: $sceneName, 是否系统场景: $isSystemScene")
    }

    override fun onStart() {
        super.onStart()
    }

    override fun getDialogHeight(): Int {
        // 场景管理对话框的预估高度：约250dp
        return dpToPx(260)
    }



}