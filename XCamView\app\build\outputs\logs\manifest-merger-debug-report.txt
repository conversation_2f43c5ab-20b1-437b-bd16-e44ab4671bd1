-- Merging decision tree log ---
manifest
ADDED from C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:2:1-51:12
INJECTED from C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:2:1-51:12
INJECTED from C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:2:1-51:12
INJECTED from C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:2:1-51:12
MERGED from [CodecUtils.aar] C:\Users\<USER>\.gradle\caches\transforms-4\b0f775f00dbcf1da11648a9d24df52d2\transformed\CodecUtils\AndroidManifest.xml:2:1-27:12
MERGED from [androidx.databinding:viewbinding:8.6.0] C:\Users\<USER>\.gradle\caches\transforms-4\8233c9fd482ab8989690a30c1bb258a8\transformed\viewbinding-8.6.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\transforms-4\2052e42c61716abd4b6d39c7be558473\transformed\material-1.10.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-4\c8baa55282e0ad49cff784f2a0382e13\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\0d1d21a82adfbc6868fdc294e3de0b55\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\560db8d7606c3580fcf519784f6f6a65\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment-ktx:1.5.6] C:\Users\<USER>\.gradle\caches\transforms-4\04e63a28e4a938247e8c61f3ec1892ac\transformed\fragment-ktx-1.5.6\AndroidManifest.xml:17:1-24:12
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\transforms-4\fabdc06b987d092fd72e55b8f4356fee\transformed\glide-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-4\7e6fcebd5770ccc53d4256ba18888aa1\transformed\recyclerview-1.3.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\transforms-4\1817f427727de7576c96f1aeb3a62adf\transformed\viewpager2-1.1.0-beta02\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.fragment:fragment:1.5.6] C:\Users\<USER>\.gradle\caches\transforms-4\66c77896d1284ff695e95fec3b2a5572\transformed\fragment-1.5.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\66e7e2c333d8b666788a5a78a605d775\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\912c807adf5a57eb3e906bf09edd6568\transformed\emoji2-1.2.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\27b5dfbed30c8889b1b088891c87dce7\transformed\activity-ktx-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\7d1a857b5e49c838a1de243173bfd141\transformed\activity-1.8.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\90267f74d9ba9dbb5ba02992fc5ae537\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\75da82597bef52effebe89eef35060f3\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\4679422fdc1c05f11ecb3bb4c42ebed4\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\9ba0928a4a774cda5bd6c42f5f9bf807\transformed\transition-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\f7031b39d1cdfeca73b4fe928af47b4a\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\98f9cea7435ab89104c000c8e747c5b3\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\58619e3f7bc72582e22b82b9a7f0ad3b\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\eeac1dd03c537bd1c67f9f5bddd60587\transformed\core-ktx-1.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\bbcb6d18c4b5560a8e247db50fc61761\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\a7e6ba9dc77083b3e9748478576df0b3\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\751793f1ef86a68442773547551f9d97\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7f5dd1ba8d6456e4b7e7637c449709a7\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\be4ee3b3d380c351331d5de220cf8d41\transformed\core-1.9.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\62019c551ab85440058cebf706298e17\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\0f878b9f795ffdba41a01918b286f62f\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\894e98f07b80693443da336ce5440bbd\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\fa1399f05079a93f21967947f1093fe2\transformed\lifecycle-runtime-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\510007efd8f0d28bfad7efb4324a67b4\transformed\lifecycle-viewmodel-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\2659dca782c3902a5a89c540359c7180\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\d2c35229f70d5948a16359761d5edd14\transformed\lifecycle-livedata-core-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\35287b3fa860d6767077178ee6b7f6e2\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\65780193b657b02600c41f9f5b4d2448\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\f3e415d3a6d69a09c0a72f7c8de40c5c\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\00a865572ad8e33aedd6e28296b808e7\transformed\lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [io.github.beyka:Android-TiffBitmapFactory:0.9.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\721038d08af86c4b905c1fdb9a9cfaf9\transformed\Android-TiffBitmapFactory-0.9.9.1\AndroidManifest.xml:2:1-14:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\d6d01aea875d7fcf428d7d91f843c46c\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\1627acc086147fedbce1f6ef422ce0ad\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\transforms-4\87feea3bcfa2ca473e17c3bc2657cff1\transformed\gifdecoder-4.16.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-4\17387289fbc541d178759e8fd9e47163\transformed\exifinterface-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\7683e6623f643d5f6a37aaa9b4be4fa0\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\19dd1c1f2e6dcef0544530e57bfc4489\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\0329c493a97fca112a4a83c1dff3ce04\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\c11985572633ec98a54dbb1d63c7b581\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\d59c28fe21e5eecd6ce51109798bdcf2\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\d327a1159c42fa0003a567224188d77d\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\65e611c0391ad2e54cfef2317326d58e\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\05c8a262799fd1f77d9c84ecdf9d7374\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\c1514a6102a9b13831f14b04199ad0ae\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:17:1-22:12
	package
		INJECTED from C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml
	android:sharedUserId
		ADDED from C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:4:5-46
	android:versionName
		INJECTED from C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:5:5-51
	android:versionCode
		INJECTED from C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:3:5-63
uses-permission#android.permission.CAMERA
ADDED from C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:7:5-65
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:7:22-62
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:8:5-81
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:8:22-78
uses-permission#android.permission.RECORD_AUDIO
ADDED from C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:9:5-71
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:9:22-68
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:10:5-80
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:10:22-77
uses-permission#android.permission.MANAGE_EXTERNAL_STORAGE
ADDED from C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:11:5-82
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:11:22-79
uses-permission#android.permission.VIBRATE
ADDED from C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:12:5-66
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:12:22-63
application
ADDED from C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:14:5-49:19
INJECTED from C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:14:5-49:19
MERGED from [CodecUtils.aar] C:\Users\<USER>\.gradle\caches\transforms-4\b0f775f00dbcf1da11648a9d24df52d2\transformed\CodecUtils\AndroidManifest.xml:7:5-25:19
MERGED from [CodecUtils.aar] C:\Users\<USER>\.gradle\caches\transforms-4\b0f775f00dbcf1da11648a9d24df52d2\transformed\CodecUtils\AndroidManifest.xml:7:5-25:19
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\transforms-4\2052e42c61716abd4b6d39c7be558473\transformed\material-1.10.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\transforms-4\2052e42c61716abd4b6d39c7be558473\transformed\material-1.10.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-4\c8baa55282e0ad49cff784f2a0382e13\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-4\c8baa55282e0ad49cff784f2a0382e13\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\912c807adf5a57eb3e906bf09edd6568\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\912c807adf5a57eb3e906bf09edd6568\transformed\emoji2-1.2.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\be4ee3b3d380c351331d5de220cf8d41\transformed\core-1.9.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\be4ee3b3d380c351331d5de220cf8d41\transformed\core-1.9.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\2659dca782c3902a5a89c540359c7180\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\2659dca782c3902a5a89c540359c7180\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:23:5-33:19
MERGED from [io.github.beyka:Android-TiffBitmapFactory:0.9.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\721038d08af86c4b905c1fdb9a9cfaf9\transformed\Android-TiffBitmapFactory-0.9.9.1\AndroidManifest.xml:9:5-12:19
MERGED from [io.github.beyka:Android-TiffBitmapFactory:0.9.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\721038d08af86c4b905c1fdb9a9cfaf9\transformed\Android-TiffBitmapFactory-0.9.9.1\AndroidManifest.xml:9:5-12:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\7683e6623f643d5f6a37aaa9b4be4fa0\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\7683e6623f643d5f6a37aaa9b4be4fa0\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\0329c493a97fca112a4a83c1dff3ce04\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\0329c493a97fca112a4a83c1dff3ce04\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
	android:extractNativeLibs
		INJECTED from C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\be4ee3b3d380c351331d5de220cf8d41\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:21:9-35
	android:label
		ADDED from C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:19:9-33
		REJECTED from [io.github.beyka:Android-TiffBitmapFactory:0.9.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\721038d08af86c4b905c1fdb9a9cfaf9\transformed\Android-TiffBitmapFactory-0.9.9.1\AndroidManifest.xml:11:9-41
	android:fullBackupContent
		ADDED from C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:17:9-54
	android:roundIcon
		ADDED from C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:20:9-54
	tools:targetApi
		ADDED from C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:23:9-29
	android:icon
		ADDED from C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:18:9-43
	android:allowBackup
		ADDED from C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:15:9-35
		REJECTED from [io.github.beyka:Android-TiffBitmapFactory:0.9.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\721038d08af86c4b905c1fdb9a9cfaf9\transformed\Android-TiffBitmapFactory-0.9.9.1\AndroidManifest.xml:10:9-36
	android:theme
		ADDED from C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:22:9-68
	android:dataExtractionRules
		ADDED from C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:16:9-65
	tools:replace
		ADDED from C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:24:9-58
activity#com.touptek.xcamview.activity.MainActivity
ADDED from C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:30:9-43:20
	android:screenOrientation
		ADDED from C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:36:13-52
	android:launchMode
		ADDED from C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:34:13-44
	android:exported
		ADDED from C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:33:13-36
	android:supportsPictureInPicture
		ADDED from C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:37:13-52
	android:resizeableActivity
		ADDED from C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:35:13-46
	android:configChanges
		ADDED from C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:32:13-115
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:31:13-70
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.DEFAULT+category:name:android.intent.category.LAUNCHER
ADDED from C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:38:13-42:29
action#android.intent.action.MAIN
ADDED from C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:39:17-69
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:39:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:40:17-77
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:40:27-74
category#android.intent.category.DEFAULT
ADDED from C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:41:17-76
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:41:27-73
activity#com.touptek.xcamview.activity.videomanagement.TpVideoEncoderActivity
ADDED from C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:45:9-85
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:45:19-82
activity#com.touptek.xcamview.activity.videomanagement.TpVideoDecoderActivity
ADDED from C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:46:9-85
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:46:19-82
activity#com.touptek.xcamview.activity.browse.TpVideoBrowse
ADDED from C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:47:9-67
	android:name
		ADDED from C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml:47:19-64
uses-sdk
INJECTED from C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml
INJECTED from C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml
MERGED from [CodecUtils.aar] C:\Users\<USER>\.gradle\caches\transforms-4\b0f775f00dbcf1da11648a9d24df52d2\transformed\CodecUtils\AndroidManifest.xml:5:5-44
MERGED from [CodecUtils.aar] C:\Users\<USER>\.gradle\caches\transforms-4\b0f775f00dbcf1da11648a9d24df52d2\transformed\CodecUtils\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.6.0] C:\Users\<USER>\.gradle\caches\transforms-4\8233c9fd482ab8989690a30c1bb258a8\transformed\viewbinding-8.6.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.6.0] C:\Users\<USER>\.gradle\caches\transforms-4\8233c9fd482ab8989690a30c1bb258a8\transformed\viewbinding-8.6.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\transforms-4\2052e42c61716abd4b6d39c7be558473\transformed\material-1.10.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.10.0] C:\Users\<USER>\.gradle\caches\transforms-4\2052e42c61716abd4b6d39c7be558473\transformed\material-1.10.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-4\c8baa55282e0ad49cff784f2a0382e13\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-4\c8baa55282e0ad49cff784f2a0382e13\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\0d1d21a82adfbc6868fdc294e3de0b55\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\0d1d21a82adfbc6868fdc294e3de0b55\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\560db8d7606c3580fcf519784f6f6a65\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\560db8d7606c3580fcf519784f6f6a65\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment-ktx:1.5.6] C:\Users\<USER>\.gradle\caches\transforms-4\04e63a28e4a938247e8c61f3ec1892ac\transformed\fragment-ktx-1.5.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment-ktx:1.5.6] C:\Users\<USER>\.gradle\caches\transforms-4\04e63a28e4a938247e8c61f3ec1892ac\transformed\fragment-ktx-1.5.6\AndroidManifest.xml:20:5-22:41
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\transforms-4\fabdc06b987d092fd72e55b8f4356fee\transformed\glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:glide:4.16.0] C:\Users\<USER>\.gradle\caches\transforms-4\fabdc06b987d092fd72e55b8f4356fee\transformed\glide-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-4\7e6fcebd5770ccc53d4256ba18888aa1\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-4\7e6fcebd5770ccc53d4256ba18888aa1\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\transforms-4\1817f427727de7576c96f1aeb3a62adf\transformed\viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\transforms-4\1817f427727de7576c96f1aeb3a62adf\transformed\viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [androidx.fragment:fragment:1.5.6] C:\Users\<USER>\.gradle\caches\transforms-4\66c77896d1284ff695e95fec3b2a5572\transformed\fragment-1.5.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.6] C:\Users\<USER>\.gradle\caches\transforms-4\66c77896d1284ff695e95fec3b2a5572\transformed\fragment-1.5.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\66e7e2c333d8b666788a5a78a605d775\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\66e7e2c333d8b666788a5a78a605d775\transformed\emoji2-views-helper-1.2.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\912c807adf5a57eb3e906bf09edd6568\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\912c807adf5a57eb3e906bf09edd6568\transformed\emoji2-1.2.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\27b5dfbed30c8889b1b088891c87dce7\transformed\activity-ktx-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\27b5dfbed30c8889b1b088891c87dce7\transformed\activity-ktx-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\7d1a857b5e49c838a1de243173bfd141\transformed\activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\7d1a857b5e49c838a1de243173bfd141\transformed\activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\90267f74d9ba9dbb5ba02992fc5ae537\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\90267f74d9ba9dbb5ba02992fc5ae537\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\75da82597bef52effebe89eef35060f3\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\75da82597bef52effebe89eef35060f3\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\4679422fdc1c05f11ecb3bb4c42ebed4\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\4679422fdc1c05f11ecb3bb4c42ebed4\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\9ba0928a4a774cda5bd6c42f5f9bf807\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\9ba0928a4a774cda5bd6c42f5f9bf807\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\f7031b39d1cdfeca73b4fe928af47b4a\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\f7031b39d1cdfeca73b4fe928af47b4a\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\98f9cea7435ab89104c000c8e747c5b3\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\98f9cea7435ab89104c000c8e747c5b3\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\58619e3f7bc72582e22b82b9a7f0ad3b\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\58619e3f7bc72582e22b82b9a7f0ad3b\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\eeac1dd03c537bd1c67f9f5bddd60587\transformed\core-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\eeac1dd03c537bd1c67f9f5bddd60587\transformed\core-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\bbcb6d18c4b5560a8e247db50fc61761\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\bbcb6d18c4b5560a8e247db50fc61761\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\a7e6ba9dc77083b3e9748478576df0b3\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\a7e6ba9dc77083b3e9748478576df0b3\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\751793f1ef86a68442773547551f9d97\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\751793f1ef86a68442773547551f9d97\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7f5dd1ba8d6456e4b7e7637c449709a7\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7f5dd1ba8d6456e4b7e7637c449709a7\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\be4ee3b3d380c351331d5de220cf8d41\transformed\core-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\be4ee3b3d380c351331d5de220cf8d41\transformed\core-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\62019c551ab85440058cebf706298e17\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\62019c551ab85440058cebf706298e17\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\0f878b9f795ffdba41a01918b286f62f\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\0f878b9f795ffdba41a01918b286f62f\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\894e98f07b80693443da336ce5440bbd\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\894e98f07b80693443da336ce5440bbd\transformed\lifecycle-livedata-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\fa1399f05079a93f21967947f1093fe2\transformed\lifecycle-runtime-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\fa1399f05079a93f21967947f1093fe2\transformed\lifecycle-runtime-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\510007efd8f0d28bfad7efb4324a67b4\transformed\lifecycle-viewmodel-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\510007efd8f0d28bfad7efb4324a67b4\transformed\lifecycle-viewmodel-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\2659dca782c3902a5a89c540359c7180\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\2659dca782c3902a5a89c540359c7180\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\d2c35229f70d5948a16359761d5edd14\transformed\lifecycle-livedata-core-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\d2c35229f70d5948a16359761d5edd14\transformed\lifecycle-livedata-core-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\35287b3fa860d6767077178ee6b7f6e2\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\35287b3fa860d6767077178ee6b7f6e2\transformed\lifecycle-livedata-core-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\65780193b657b02600c41f9f5b4d2448\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\65780193b657b02600c41f9f5b4d2448\transformed\lifecycle-viewmodel-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\f3e415d3a6d69a09c0a72f7c8de40c5c\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\f3e415d3a6d69a09c0a72f7c8de40c5c\transformed\lifecycle-runtime-2.6.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\00a865572ad8e33aedd6e28296b808e7\transformed\lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\00a865572ad8e33aedd6e28296b808e7\transformed\lifecycle-viewmodel-savedstate-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [io.github.beyka:Android-TiffBitmapFactory:0.9.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\721038d08af86c4b905c1fdb9a9cfaf9\transformed\Android-TiffBitmapFactory-0.9.9.1\AndroidManifest.xml:5:5-7:41
MERGED from [io.github.beyka:Android-TiffBitmapFactory:0.9.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\721038d08af86c4b905c1fdb9a9cfaf9\transformed\Android-TiffBitmapFactory-0.9.9.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\d6d01aea875d7fcf428d7d91f843c46c\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\d6d01aea875d7fcf428d7d91f843c46c\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\1627acc086147fedbce1f6ef422ce0ad\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\1627acc086147fedbce1f6ef422ce0ad\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\transforms-4\87feea3bcfa2ca473e17c3bc2657cff1\transformed\gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.16.0] C:\Users\<USER>\.gradle\caches\transforms-4\87feea3bcfa2ca473e17c3bc2657cff1\transformed\gifdecoder-4.16.0\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-4\17387289fbc541d178759e8fd9e47163\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-4\17387289fbc541d178759e8fd9e47163\transformed\exifinterface-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\7683e6623f643d5f6a37aaa9b4be4fa0\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\7683e6623f643d5f6a37aaa9b4be4fa0\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\19dd1c1f2e6dcef0544530e57bfc4489\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\19dd1c1f2e6dcef0544530e57bfc4489\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\0329c493a97fca112a4a83c1dff3ce04\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\0329c493a97fca112a4a83c1dff3ce04\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\c11985572633ec98a54dbb1d63c7b581\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\c11985572633ec98a54dbb1d63c7b581\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\d59c28fe21e5eecd6ce51109798bdcf2\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\d59c28fe21e5eecd6ce51109798bdcf2\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\d327a1159c42fa0003a567224188d77d\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\d327a1159c42fa0003a567224188d77d\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\65e611c0391ad2e54cfef2317326d58e\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\65e611c0391ad2e54cfef2317326d58e\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\05c8a262799fd1f77d9c84ecdf9d7374\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\05c8a262799fd1f77d9c84ecdf9d7374\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\c1514a6102a9b13831f14b04199ad0ae\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\c1514a6102a9b13831f14b04199ad0ae\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
	android:targetSdkVersion
		INJECTED from C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\hhx\rk3588\AndroidStudio\XCamView\app\src\main\AndroidManifest.xml
activity#com.touptek.ui.compare.TpImageCompareActivity
ADDED from [CodecUtils.aar] C:\Users\<USER>\.gradle\caches\transforms-4\b0f775f00dbcf1da11648a9d24df52d2\transformed\CodecUtils\AndroidManifest.xml:10:9-14:66
	android:screenOrientation
		ADDED from [CodecUtils.aar] C:\Users\<USER>\.gradle\caches\transforms-4\b0f775f00dbcf1da11648a9d24df52d2\transformed\CodecUtils\AndroidManifest.xml:13:13-50
	android:exported
		ADDED from [CodecUtils.aar] C:\Users\<USER>\.gradle\caches\transforms-4\b0f775f00dbcf1da11648a9d24df52d2\transformed\CodecUtils\AndroidManifest.xml:12:13-37
	android:theme
		ADDED from [CodecUtils.aar] C:\Users\<USER>\.gradle\caches\transforms-4\b0f775f00dbcf1da11648a9d24df52d2\transformed\CodecUtils\AndroidManifest.xml:14:13-63
	android:name
		ADDED from [CodecUtils.aar] C:\Users\<USER>\.gradle\caches\transforms-4\b0f775f00dbcf1da11648a9d24df52d2\transformed\CodecUtils\AndroidManifest.xml:11:13-73
activity#com.touptek.ui.compare.TpImageCompareTripleActivity
ADDED from [CodecUtils.aar] C:\Users\<USER>\.gradle\caches\transforms-4\b0f775f00dbcf1da11648a9d24df52d2\transformed\CodecUtils\AndroidManifest.xml:15:9-19:66
	android:screenOrientation
		ADDED from [CodecUtils.aar] C:\Users\<USER>\.gradle\caches\transforms-4\b0f775f00dbcf1da11648a9d24df52d2\transformed\CodecUtils\AndroidManifest.xml:18:13-50
	android:exported
		ADDED from [CodecUtils.aar] C:\Users\<USER>\.gradle\caches\transforms-4\b0f775f00dbcf1da11648a9d24df52d2\transformed\CodecUtils\AndroidManifest.xml:17:13-37
	android:theme
		ADDED from [CodecUtils.aar] C:\Users\<USER>\.gradle\caches\transforms-4\b0f775f00dbcf1da11648a9d24df52d2\transformed\CodecUtils\AndroidManifest.xml:19:13-63
	android:name
		ADDED from [CodecUtils.aar] C:\Users\<USER>\.gradle\caches\transforms-4\b0f775f00dbcf1da11648a9d24df52d2\transformed\CodecUtils\AndroidManifest.xml:16:13-79
activity#com.touptek.ui.compare.TpImageCompareMultiActivity
ADDED from [CodecUtils.aar] C:\Users\<USER>\.gradle\caches\transforms-4\b0f775f00dbcf1da11648a9d24df52d2\transformed\CodecUtils\AndroidManifest.xml:20:9-24:66
	android:screenOrientation
		ADDED from [CodecUtils.aar] C:\Users\<USER>\.gradle\caches\transforms-4\b0f775f00dbcf1da11648a9d24df52d2\transformed\CodecUtils\AndroidManifest.xml:23:13-50
	android:exported
		ADDED from [CodecUtils.aar] C:\Users\<USER>\.gradle\caches\transforms-4\b0f775f00dbcf1da11648a9d24df52d2\transformed\CodecUtils\AndroidManifest.xml:22:13-37
	android:theme
		ADDED from [CodecUtils.aar] C:\Users\<USER>\.gradle\caches\transforms-4\b0f775f00dbcf1da11648a9d24df52d2\transformed\CodecUtils\AndroidManifest.xml:24:13-63
	android:name
		ADDED from [CodecUtils.aar] C:\Users\<USER>\.gradle\caches\transforms-4\b0f775f00dbcf1da11648a9d24df52d2\transformed\CodecUtils\AndroidManifest.xml:21:13-78
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\912c807adf5a57eb3e906bf09edd6568\transformed\emoji2-1.2.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\2659dca782c3902a5a89c540359c7180\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\2659dca782c3902a5a89c540359c7180\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\7683e6623f643d5f6a37aaa9b4be4fa0\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\7683e6623f643d5f6a37aaa9b4be4fa0\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\912c807adf5a57eb3e906bf09edd6568\transformed\emoji2-1.2.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\912c807adf5a57eb3e906bf09edd6568\transformed\emoji2-1.2.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\912c807adf5a57eb3e906bf09edd6568\transformed\emoji2-1.2.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\912c807adf5a57eb3e906bf09edd6568\transformed\emoji2-1.2.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\912c807adf5a57eb3e906bf09edd6568\transformed\emoji2-1.2.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\912c807adf5a57eb3e906bf09edd6568\transformed\emoji2-1.2.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\912c807adf5a57eb3e906bf09edd6568\transformed\emoji2-1.2.0\AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\be4ee3b3d380c351331d5de220cf8d41\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\be4ee3b3d380c351331d5de220cf8d41\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\be4ee3b3d380c351331d5de220cf8d41\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
permission#com.touptek.xcamview.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\be4ee3b3d380c351331d5de220cf8d41\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\be4ee3b3d380c351331d5de220cf8d41\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\be4ee3b3d380c351331d5de220cf8d41\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\be4ee3b3d380c351331d5de220cf8d41\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\be4ee3b3d380c351331d5de220cf8d41\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
uses-permission#com.touptek.xcamview.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\be4ee3b3d380c351331d5de220cf8d41\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.9.0] C:\Users\<USER>\.gradle\caches\transforms-4\be4ee3b3d380c351331d5de220cf8d41\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\2659dca782c3902a5a89c540359c7180\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\2659dca782c3902a5a89c540359c7180\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-4\2659dca782c3902a5a89c540359c7180\transformed\lifecycle-process-2.6.1\AndroidManifest.xml:30:17-78
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\be64d76594b12ee2fe263feba7de7179\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
