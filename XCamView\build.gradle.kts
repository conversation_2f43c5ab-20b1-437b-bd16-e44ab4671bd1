// Top-level build file where you can add configuration options common to all sub-projects/modules.
plugins {
    alias(libs.plugins.android.application) apply false
//    id 'org.jetbrains.kotlin.android' version '1.8.0' apply false
}

allprojects {
    repositories {
        // 在这里添加你所需要的仓库地址
    }

    tasks.withType<JavaCompile> {
        options.compilerArgs.add("-Xbootclasspath/p:app/libs/framework.jar")
    }
}
