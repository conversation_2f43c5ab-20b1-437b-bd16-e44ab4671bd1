package com.touptek.xcamview.util

import android.content.res.Resources
import androidx.appcompat.app.AppCompatActivity
import android.view.WindowManager

fun Int.dpToPx(): Int = (this * Resources.getSystem().displayMetrics.density).toInt()

fun AppCompatActivity.setupEdgeToEdgeFullScreen() {
    // 使用传统的全屏方法
    window.setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN,
                   WindowManager.LayoutParams.FLAG_FULLSCREEN)

}

