package com.touptek.xcamview.activity.browse.videomanagement

import android.Manifest
import android.content.pm.PackageManager
import android.graphics.SurfaceTexture
import android.os.Bundle
import android.os.Handler
import android.util.Log
import android.view.GestureDetector
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.ScaleGestureDetector
import android.view.Surface
import android.view.TextureView
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.SeekBar
import android.widget.TextView
import androidx.core.app.ActivityCompat
import androidx.fragment.app.DialogFragment
import com.touptek.xcamview.R
import com.touptek.ui.internal.TpViewTransform
import com.touptek.utils.TpFileManager
import com.touptek.video.internal.TpVideoDecoder
import kotlin.apply
import kotlin.collections.isNotEmpty
import kotlin.let
import kotlin.math.max
import kotlin.math.min
import kotlin.text.format

class TpVideoDecoderDialogFragment : DialogFragment() {

    companion object {
        private const val TAG = "VideoDecoderDialog"
        private const val ARG_VIDEO_PATH = "video_path"

        fun newInstance(videoPath: String): TpVideoDecoderDialogFragment {
            val args = Bundle().apply {
                putString(ARG_VIDEO_PATH, videoPath)
            }
            return TpVideoDecoderDialogFragment().apply {
                arguments = args
            }
        }
    }

    private lateinit var textureView: TextureView
    private lateinit var seekBar: SeekBar
    private lateinit var playPauseButton: Button
    private lateinit var frameByFrameButton: Button
    private lateinit var fastForwardButton: Button
    private lateinit var fastBackwardButton: Button
    private lateinit var returnbtn: Button
    private lateinit var tvDuration: TextView
    private lateinit var tvCurrentPosition: TextView

    private var tpVideoDecoder: TpVideoDecoder? = null
    private val handler = Handler()
    private var lastSeekPosition: Long = 0L
    private var lastSeekUpdateTime: Long = 0L

    private var scaleGestureDetector: ScaleGestureDetector? = null
    private var gestureDetector: GestureDetector? = null

    private var isLongPress: Boolean = false
    private val longPressThreshold = 500L // 长按触发阈值（毫秒）
    private val repeatInterval = 50L     // 连续触发间隔（毫秒）
    private val longPressRunnable = Runnable {
        isLongPress = true
        startRepeatingStep()
    }

    private val updateSeekBarRunnable = object : Runnable {
        override fun run() {
            tpVideoDecoder?.let { decoder ->
                if (decoder.isDecoding) {
                    /* 更新UI显示 */
                    updatePlayerUI()
                    /* 检查是否暂停或播放完成 */
                    if (!tpVideoDecoder!!.isPaused && !tpVideoDecoder!!.isPlaybackCompleted) {
                        /* 每秒更新一次进度条 */
                        handler.postDelayed(this, 1000)
                    }
                }
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(STYLE_NORMAL, R.style.FullScreenDialog)
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.videodecode_layout, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        textureView = view.findViewById(R.id.surface_view)
        seekBar = view.findViewById(R.id.seek_bar)
        playPauseButton = view.findViewById(R.id.btn_play_pause)
        frameByFrameButton = view.findViewById(R.id.btn_step_decode)
        fastForwardButton = view.findViewById(R.id.btn_fast_forward)
        fastBackwardButton = view.findViewById(R.id.btn_fast_backward)
        returnbtn = view.findViewById(R.id.btn_decode_return)
        tvDuration = view.findViewById(R.id.tv_duration)
        tvCurrentPosition = view.findViewById(R.id.tv_current_position)

        setupVideoPlayback()
        setupControls()
        requestStoragePermission()
    }

    private fun setupVideoPlayback() {
        textureView.surfaceTextureListener = object : TextureView.SurfaceTextureListener {
            override fun onSurfaceTextureAvailable(surface: SurfaceTexture, width: Int, height: Int) {
                val videoPath = arguments?.getString(ARG_VIDEO_PATH) ?: TpFileManager.createVideoPath(requireContext())
                tpVideoDecoder = TpVideoDecoder(videoPath, Surface(surface)).apply {
                    startDecoding()
                }
                handler.post(updateSeekBarRunnable)
                updatePlayButton(true)
                tvDuration.text = "总时长: ${formatTime(tpVideoDecoder?.videoDuration ?: 0)}"
            }

            override fun onSurfaceTextureSizeChanged(surface: SurfaceTexture, width: Int, height: Int) {}
            override fun onSurfaceTextureDestroyed(surface: SurfaceTexture): Boolean {
                tpVideoDecoder?.stopDecoding()
                handler.removeCallbacks(updateSeekBarRunnable)
                return true
            }

            override fun onSurfaceTextureUpdated(surface: SurfaceTexture) {}
        }
    }

    private fun setupControls() {
        seekBar.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar, progress: Int, fromUser: Boolean) {
                if (fromUser) {
                    tpVideoDecoder?.let {
                        val newPosition = it.videoDuration * progress / 100
                        it.seekTo(newPosition)
                        lastSeekPosition = newPosition
                        lastSeekUpdateTime = System.currentTimeMillis()
                        tvCurrentPosition.text = "当前时长: ${formatTime(newPosition)}"
                    }
                }
            }

            override fun onStartTrackingTouch(seekBar: SeekBar) {}
            override fun onStopTrackingTouch(seekBar: SeekBar) {}
        })

        playPauseButton.setOnClickListener {
            tpVideoDecoder?.let {
                it.togglePlayPause()
                updatePlayButton(!it.isPaused)
                if (!it.isPaused) {
                    lastSeekUpdateTime = 0
                    handler.post(updateSeekBarRunnable)
                }
            }
        }

        frameByFrameButton.setOnTouchListener { v, event ->
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    isLongPress = false
                    handler.postDelayed(longPressRunnable, longPressThreshold)
                    true
                }
                MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                    handler.removeCallbacks(longPressRunnable)
                    handler.removeCallbacks(::startRepeatingStep)
                    if (isLongPress) {
                        isLongPress = false
                    } else {
                        tpVideoDecoder?.stepFrame()
                        updatePlayButton(false)
                    }
                    true
                }
                else -> false
            }
        }

        returnbtn.setOnClickListener {
            dismiss()
        }

        fastForwardButton.setOnClickListener {
            tpVideoDecoder?.seekRelative(5000)
            updatePlayerUI()

            /* 如果处于暂停状态，让解码器解码新位置的帧 */
            if (tpVideoDecoder!!.isPaused) {
                tpVideoDecoder!!.stepFrame()
            }
        }

        fastBackwardButton.setOnClickListener {
            tpVideoDecoder?.seekRelative(-5000)
            updatePlayerUI()

            /* 如果处于暂停状态，让解码器解码新位置的帧 */
            if (tpVideoDecoder!!.isPaused) {
                tpVideoDecoder!!.stepFrame()
            }
        }

        scaleGestureDetector = ScaleGestureDetector(
            requireContext(),
            object : ScaleGestureDetector.SimpleOnScaleGestureListener() {
                override fun onScale(detector: ScaleGestureDetector): Boolean {
                    val scaleFactor = detector.scaleFactor
                    val focusX = detector.focusX
                    val focusY = detector.focusY
                    TpViewTransform.applyZoom(textureView, scaleFactor, focusX, focusY)
                    return true
                }
            })

        gestureDetector =
            GestureDetector(requireContext(), object : GestureDetector.SimpleOnGestureListener() {})

        textureView.setOnTouchListener { _, event ->
            scaleGestureDetector?.onTouchEvent(event) ?: false
            gestureDetector?.onTouchEvent(event) ?: false
            true
        }
    }

    private fun requestStoragePermission() {
        if (ActivityCompat.checkSelfPermission(
                requireContext(),
                Manifest.permission.READ_EXTERNAL_STORAGE
            ) != PackageManager.PERMISSION_GRANTED
        ) {
            requestPermissions(arrayOf(Manifest.permission.READ_EXTERNAL_STORAGE), 1)
        }
    }

    override fun onRequestPermissionsResult(
        requestCode: Int,
        permissions: Array<out String>,
        grantResults: IntArray
    ) {
        if (requestCode == 1 && grantResults.isNotEmpty() && grantResults[0] != PackageManager.PERMISSION_GRANTED) {
            Log.e(TAG, "Storage permission denied")
            dismiss()
        }
    }

    private fun updatePlayButton(playing: Boolean) {
        playPauseButton.text = if (playing) "暂停" else "播放"
    }

    private fun formatTime(timeUs: Long): String {
        val totalSeconds = timeUs / 1_000_000
        val hours = totalSeconds / 3600
        val minutes = (totalSeconds % 3600) / 60
        val seconds = totalSeconds % 60
        return String.format("%02d:%02d:%02d", hours, minutes, seconds)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        tpVideoDecoder?.stopDecoding()
        handler.removeCallbacks(updateSeekBarRunnable)
    }

    private fun updatePlayerUI() {
        if (tpVideoDecoder != null) {
            val currentPosition = tpVideoDecoder!!.currentPosition
            val duration = tpVideoDecoder!!.videoDuration

            /* 更新当前时间显示 */
            tvCurrentPosition.text = "当前时长: " + formatTime(currentPosition)

            /* 更新进度条 */
            if (duration > 0 && currentPosition >= 0) {
                var progress = (currentPosition * 100 / duration).toInt()
                /* 限制进度在有效范围内 */
                progress = max(0.0, min(100.0, progress.toDouble())).toInt()
                seekBar.progress = progress
            }
        }
    }

    private fun startRepeatingStep() {
        if (!isLongPress) return // 关键修复：检查长按状态

        tpVideoDecoder?.stepFrame()
        updatePlayButton(false)
        handler.postDelayed({ startRepeatingStep() }, repeatInterval)
    }
}