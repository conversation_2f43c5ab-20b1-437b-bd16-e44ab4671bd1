package com.touptek.ui;

import android.content.Context;
import android.content.res.TypedArray;
import android.graphics.SurfaceTexture;
import android.os.Handler;
import android.os.Looper;
import android.util.AttributeSet;
import android.util.Log;
import android.view.Gravity;
import android.view.LayoutInflater;
import android.view.MotionEvent;
import android.view.Surface;
import android.view.TextureView;
import android.view.View;
import android.widget.FrameLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;


import com.touptek.R;
import com.touptek.ui.internal.TpCustomProgressBar;
import com.touptek.video.TpVideoConfig;
import com.touptek.video.TpVideoSystem;

/**
 * TpVideoPlayerView - 高层封装的视频播放组件
 * <p>
 * 基于双层API设计理念，为80%的客户提供简洁易用的视频播放功能。
 * 内部使用TpVideoSystem作为播放引擎，提供完整的UI封装。
 * </p>
 * 
 * <p>使用示例：</p>
 * <pre>
 * // XML中使用
 * &lt;com.android.rockchip.camera2.view.TpVideoPlayerView
 *     android:layout_width="match_parent"
 *     android:layout_height="match_parent" /&gt;
 * 
 * // 代码中使用
 * TpVideoPlayerView playerView = findViewById(R.id.video_player);
 * playerView.setVideoPath("/storage/video.mp4");
 * playerView.play();
 *
 * // 高级功能使用
 * playerView.setPlaybackSpeed(1.5f);  // 1.5倍速播放
 * playerView.stepFrame();             // 逐帧播放
 * playerView.fastForward(5);          // 快进5秒
 * </pre>
 *
 * <p><b>基础播放控制：</b></p>
 * <ul>
 *   <li>{@link #setVideoPath(String)} - 设置视频路径</li>
 *   <li>{@link #play()} - 开始播放</li>
 *   <li>{@link #pause()} - 暂停播放</li>
 *   <li>{@link #stop()} - 停止播放</li>
 *   <li>{@link #seekTo(long)} - 跳转到指定位置</li>
 *   <li>{@link #isPlaying()} - 检查播放状态</li>
 *   <li>{@link #getCurrentPosition()} - 获取当前位置</li>
 *   <li>{@link #getDuration()} - 获取视频时长</li>
 * </ul>
 *
 * <p><b>高级播放控制：</b></p>
 * <ul>
 *   <li>{@link #setPlaybackSpeed(float)} - 变速播放（0.25x-2.0x）</li>
 *   <li>{@link #stepFrame()} - 逐帧播放</li>
 *   <li>{@link #seekRelative(long)} - 相对跳转</li>
 *   <li>{@link #fastForward(int)} - 快进指定秒数</li>
 *   <li>{@link #fastRewind(int)} - 快退指定秒数</li>
 *   <li>{@link #resetToStart()} - 重置到开头</li>
 * </ul>
 */
public class TpVideoPlayerView extends FrameLayout {

    private static final String TAG = "TpVideoPlayerView";

    // 速度选项常量
    private static final float[] PLAYBACK_SPEEDS = {2.0f, 1.5f, 1.25f, 1.0f, 0.75f, 0.5f};
    private static final String[] SPEED_TEXTS = {"2.0x", "1.5x", "1.25x", "1.0x", "0.75x", "0.5x"};

    // 核心组件
    private TpTextureView mTextureView;
    private TpVideoSystem mVideoSystem;

    // 控制界面
    private View mControlsView;
    private View mPlayPauseButton;  // 支持Button和ImageButton
    private View mStepFrameButton;
    private View mRewindButton;
    private View mForwardButton;
    private View mSettingsButton;

    // 新增的视频导航按钮
    private View mPreviousVideoButton;
    private View mNextVideoButton;

    private float mCurrentSpeed = 1.0f;
    private boolean mShowControls = true;
    private boolean mControlsVisible = true;

    // 控制界面自动隐藏
    private Handler mControlsHandler = new Handler(Looper.getMainLooper());
    private Runnable mHideControlsRunnable;
    private static final int CONTROLS_HIDE_DELAY = 5500; // 3秒后隐藏

    // 进度控制
    private TpCustomProgressBar mProgressSeekBar;
    private TextView mCurrentTimeDisplay;
    private TextView mTotalTimeDisplay;
    private Handler mProgressHandler = new Handler(Looper.getMainLooper());
    private Runnable mProgressUpdater;
    private boolean mIsUpdatingProgress = false;
    private boolean mIsUserSeeking = false;

    // 配置属性
    private boolean mAutoPlay = false;
    private String mVideoPath;
    private int mPrimaryColor = android.graphics.Color.WHITE;

    // 状态管理
    private boolean mIsInitialized = false;
    private boolean mIsPendingPlay = false;
    private Surface mCurrentSurface = null;

    // 视频播放器监听器
    private VideoPlayerListener mVideoPlayerListener;

    // 播放状态同步
    private Handler mStatusSyncHandler = new Handler(Looper.getMainLooper());
    private Runnable mStatusSyncRunnable;
    private boolean mIsStatusSyncEnabled = false;


    public TpVideoPlayerView(@NonNull Context context) {
        this(context, null);
    }

    public TpVideoPlayerView(@NonNull Context context, @Nullable AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public TpVideoPlayerView(@NonNull Context context, @Nullable AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        initFromAttributes(attrs);
        initComponents();
        setupVideoSystem();
    }

    /**
     * 从XML属性初始化配置
     */
    private void initFromAttributes(AttributeSet attrs) {
        if (attrs != null) {
            TypedArray a = getContext().obtainStyledAttributes(attrs, R.styleable.TpVideoPlayerView);

            mAutoPlay = a.getBoolean(R.styleable.TpVideoPlayerView_autoPlay, false);

            a.recycle();
        }
    }

    /**
     * 初始化UI组件
     */
    private void initComponents() {
        // 创建TextureView作为视频渲染表面
        mTextureView = new TpTextureView(getContext());
        mTextureView.setLayoutParams(new LayoutParams(
                LayoutParams.MATCH_PARENT, LayoutParams.MATCH_PARENT));
        addView(mTextureView);

        // 设置触摸事件处理策略
        setupTouchEventHandler();

        // 添加播放控制界面
        initControlsView();

        // 初始化控制界面管理
        initControlsAutoHide();
    }

    /**
     * 初始化控制界面
     */
    private void initControlsView() {
        if (!mShowControls) {
            return;
        }

        try {
            // 加载控制界面布局
            LayoutInflater inflater = LayoutInflater.from(getContext());
            mControlsView = inflater.inflate(R.layout.tp_video_player_controls, null);

            // 设置控制界面位置（底部）
            LayoutParams controlsParams = new LayoutParams(
                    LayoutParams.MATCH_PARENT, LayoutParams.WRAP_CONTENT);
            controlsParams.gravity = Gravity.BOTTOM;
            mControlsView.setLayoutParams(controlsParams);

            addView(mControlsView);

            // 初始化控制按钮
            initControlButtons();
        } catch (Exception e) {
            Log.e(TAG, "初始化控制界面失败", e);
            mShowControls = false; // 禁用控制界面
        }
    }

    /**
     * 设置触摸事件处理策略
     */
    private void setupTouchEventHandler() {
        mTextureView.setTouchEventHandler(new TpTextureView.TouchEventHandler() {
            @Override
            public boolean onSingleTapDetected(MotionEvent event) {
                if (!mControlsVisible) {
                    showControls();
                } else {
                    hideControls();
                }
                return true;
            }

            @Override
            public void onLongPressDetected(MotionEvent event) {
                if (mControlsVisible) {
                    hideControls();
                }
            }

            @Override
            public boolean onPanGestureDetected(MotionEvent event) {
                // 处理平移手势
                return true;
            }

            @Override
            public boolean onScaleGestureDetected(MotionEvent event) {
                if (mControlsVisible) {
                    hideControls();
                }
                return true;
            }
        });
    }


    /**
     * 初始化控制界面自动隐藏
     */
    private void initControlsAutoHide() {
        mHideControlsRunnable = () -> {
            if (mControlsVisible) {
                hideControls();
            }
        };

        // 初始状态：显示控制界面，3秒后隐藏
        showControls();
    }

    /**
     * 初始化控制按钮
     */
    private void initControlButtons() {
        if (mControlsView == null) {
            Log.w(TAG, "控制界面视图为null，无法初始化按钮");
            return;
        }

        // 主要控制按钮
        mPlayPauseButton = mControlsView.findViewById(R.id.btn_play_pause);
        mStepFrameButton = mControlsView.findViewById(R.id.btn_step_frame);

        // 快进快退按钮
        mRewindButton = mControlsView.findViewById(R.id.btn_rewind);
        mForwardButton = mControlsView.findViewById(R.id.btn_forward);

        // 设置按钮
        mSettingsButton = mControlsView.findViewById(R.id.btn_settings);

        // 新增的视频导航按钮
        mPreviousVideoButton = mControlsView.findViewById(R.id.btn_previous_video);
        mNextVideoButton = mControlsView.findViewById(R.id.btn_next_video);

        // 进度控制
        mProgressSeekBar = mControlsView.findViewById(R.id.seekbar_progress);
        mCurrentTimeDisplay = mControlsView.findViewById(R.id.tv_current_time);
        mTotalTimeDisplay = mControlsView.findViewById(R.id.tv_total_time);


        // 设置按钮监听器
        setupControlListeners();

        // 更新初始状态
        updatePlayPauseButtonIcon(true);
        if (!mIsUserSeeking) {
            updateProgress();
        }
    }

    /**
     * 设置控制按钮监听器
     */
    private void setupControlListeners() {
        // 播放/暂停按钮
        if (mPlayPauseButton != null) {
            mPlayPauseButton.setOnClickListener(v -> {
                if (isPlaying()) {
                    pause();
                } else {
                    play();
                }
                // 立即更新按钮图标，确保UI响应迅速
                postDelayed(() -> updatePlayPauseButtonIcon(true), 100);
                resetControlsTimer();
            });
        }

        // 逐帧播放按钮
        if (mStepFrameButton != null) {
            mStepFrameButton.setOnClickListener(v -> {
                stepFrame();
                resetControlsTimer();
            });
        }

        // 快退按钮
        if (mRewindButton != null) {
            mRewindButton.setOnClickListener(v -> {
                fastRewind(5);
                resetControlsTimer();
            });
        }

        // 快进按钮
        if (mForwardButton != null) {
            mForwardButton.setOnClickListener(v -> {
                fastForward(5);
                resetControlsTimer();
            });
        }

        // 设置按钮
        if (mSettingsButton != null) {
            mSettingsButton.setOnClickListener(v -> {
                showSpeedSelectionMenu();
                resetControlsTimer();
            });
        }

        // 上一个视频按钮
        if (mPreviousVideoButton != null) {
            mPreviousVideoButton.setOnClickListener(v -> {
                previousVideo();
                resetControlsTimer();
            });
        }

        // 下一个视频按钮
        if (mNextVideoButton != null) {
            mNextVideoButton.setOnClickListener(v -> {
                nextVideo();
                resetControlsTimer();
            });
        }

        // 进度条监听器
        if (mProgressSeekBar != null) {
            mProgressSeekBar.setOnProgressChangeListener(new TpCustomProgressBar.OnProgressChangeListener() {
                @Override
                public void onProgressChanged(TpCustomProgressBar progressBar, int progress, boolean fromUser) {
                    if (fromUser) {
                        // 用户拖拽时实时更新时间显示
                        long duration = getDuration();
                        if (duration > 0) {
                            long position = (long) (duration * progress / 100.0);
                            updateTimeDisplay(position, duration);
                        }
                    }
                }

                @Override
                public void onStartTrackingTouch(TpCustomProgressBar progressBar) {
                    mIsUserSeeking = true;
                    stopProgressUpdater();
                }

                @Override
                public void onStopTrackingTouch(TpCustomProgressBar progressBar) {
                    mIsUserSeeking = false;
                    long duration = getDuration();
                    if (duration > 0) {
                        long position = (long) (duration * progressBar.getProgress() / 100.0);
                        seekTo(position);
                    }
                    if (isPlaying()) {
                        startProgressUpdater();
                    }
                }
            });
        }

        // 为控制界面添加触摸监听，检测用户交互
        setupControlsInteractionDetection();
    }

    /**
     * 设置控制界面交互检测
     */
    private void setupControlsInteractionDetection() {
        if (mControlsView == null) return;

        mControlsView.setOnTouchListener((v, event) -> {
            if (event.getAction() == MotionEvent.ACTION_DOWN ||
                    event.getAction() == MotionEvent.ACTION_UP) {
                resetControlsTimer();
            }
            return false;
        });
    }





    /**
     * 更新播放/暂停按钮图标
     *
     * @param withAnimation 是否使用动画效果
     */
    private void updatePlayPauseButtonIcon(boolean withAnimation) {
        if (mPlayPauseButton == null) {
            return;
        }

        boolean isCurrentlyPlaying = isPlaying();
        int iconRes = isCurrentlyPlaying ? R.drawable.ic_pause_white_24 : R.drawable.ic_play_arrow_white_24;

        if (mPlayPauseButton instanceof android.widget.ImageButton) {
            android.widget.ImageButton imageButton = (android.widget.ImageButton) mPlayPauseButton;

            // 先停止任何正在进行的动画
            imageButton.animate().cancel();

            if (withAnimation) {
                // 添加图标切换的动画效果
                imageButton.animate()
                        .scaleX(0.8f)
                        .scaleY(0.8f)
                        .setDuration(100)
                        .withEndAction(() -> {
                            // 在动画中间更新图标
                            imageButton.setImageResource(iconRes);
                            imageButton.animate()
                                    .scaleX(1.0f)
                                    .scaleY(1.0f)
                                    .setDuration(100)
                                    .start();
                        })
                        .start();
            } else {
                // 直接更新图标，无动画
                imageButton.setImageResource(iconRes);
            }


        }
    }

    /**
     * 播放上一个视频
     */
    private void previousVideo() {
        // 通知监听器切换到上一个视频
        if (mVideoPlayerListener != null) {
            mVideoPlayerListener.onPreviousVideo();
        }

    }

    /**
     * 播放下一个视频
     */
    private void nextVideo() {
        // 通知监听器切换到下一个视频
        if (mVideoPlayerListener != null) {
            mVideoPlayerListener.onNextVideo();
        }

    }

    /**
     * 显示播放速度选择菜单
     */
    private void showSpeedSelectionMenu() {
        // 创建PopupWindow下拉菜单
        LayoutInflater inflater = LayoutInflater.from(getContext());
        View dropdownView = inflater.inflate(R.layout.tp_speed_dropdown_menu, null);

        android.widget.PopupWindow speedPopup = new android.widget.PopupWindow(
                dropdownView,
                android.view.ViewGroup.LayoutParams.WRAP_CONTENT,
                android.view.ViewGroup.LayoutParams.WRAP_CONTENT,
                true
        );

        // 设置PopupWindow属性
        speedPopup.setOutsideTouchable(true);
        speedPopup.setFocusable(true);
        speedPopup.setElevation(8);

        // 设置进入和退出动画
        speedPopup.setAnimationStyle(R.style.TpVideoSpeedDropdownAnimation);

        // 获取速度选项视图
        TextView speed05 = dropdownView.findViewById(R.id.speed_0_5);
        TextView speed075 = dropdownView.findViewById(R.id.speed_0_75);
        TextView speed10 = dropdownView.findViewById(R.id.speed_1_0);
        TextView speed125 = dropdownView.findViewById(R.id.speed_1_25);
        TextView speed15 = dropdownView.findViewById(R.id.speed_1_5);
        TextView speed20 = dropdownView.findViewById(R.id.speed_2_0);

        // 速度选项数组（注意：布局中是从快到慢排列）
        TextView[] speedViews = {speed20, speed15, speed125, speed10, speed075, speed05};

        // 更新选中状态显示
        updateSpeedSelectionDisplay(speedViews, PLAYBACK_SPEEDS, SPEED_TEXTS);

        // 设置点击监听器
        for (int i = 0; i < speedViews.length; i++) {
            final float speed = PLAYBACK_SPEEDS[i];
            speedViews[i].setOnClickListener(v -> {
                // 设置播放速度
                setPlaybackSpeed(speed);

                // 更新显示状态
                updateSpeedSelectionDisplay(speedViews, PLAYBACK_SPEEDS, SPEED_TEXTS);

                // 添加点击反馈动画
                v.animate()
                        .scaleX(0.95f)
                        .scaleY(0.95f)
                        .setDuration(100)
                        .withEndAction(() -> {
                            v.animate()
                                    .scaleX(1.0f)
                                    .scaleY(1.0f)
                                    .setDuration(100)
                                    .withEndAction(() -> {
                                        // 延迟关闭菜单，让用户看到选择效果
                                        new Handler(Looper.getMainLooper())
                                                .postDelayed(speedPopup::dismiss, 150);
                                    })
                                    .start();
                        })
                        .start();


            });
        }

        // 计算显示位置（在设置按钮上方）
        int[] location = new int[2];
        mSettingsButton.getLocationOnScreen(location);

        // 测量PopupWindow尺寸
        dropdownView.measure(
                MeasureSpec.makeMeasureSpec(0, MeasureSpec.UNSPECIFIED),
                MeasureSpec.makeMeasureSpec(0, MeasureSpec.UNSPECIFIED)
        );

        int popupWidth = dropdownView.getMeasuredWidth();
        int popupHeight = dropdownView.getMeasuredHeight();

        // 计算显示位置（设置按钮上方，水平居中）
        int xOffset = (mSettingsButton.getWidth() - popupWidth) / 2;
        int yOffset = -popupHeight - 8; // 在按钮上方8dp处

        // 显示PopupWindow
        speedPopup.showAsDropDown(mSettingsButton, xOffset, yOffset);


    }

    /**
     * 更新速度选择显示状态
     */
    private void updateSpeedSelectionDisplay(TextView[] speedViews,
                                             float[] speeds, String[] speedTexts) {
        for (int i = 0; i < speedViews.length; i++) {
            boolean isSelected = Math.abs(speeds[i] - mCurrentSpeed) < 0.01f;

            if (isSelected) {
                // 选中状态：添加✓符号和特殊背景
                speedViews[i].setText(speedTexts[i] + " ✓");
                speedViews[i].setBackgroundResource(R.drawable.tp_speed_item_selected_background);
            } else {
                // 未选中状态：只显示速度和普通背景
                speedViews[i].setText(speedTexts[i]);
                speedViews[i].setBackgroundResource(R.drawable.tp_speed_item_background);
            }
        }
    }

    /**
     * 获取当前播放速度对应的索引
     */
    private int getSpeedIndex(float speed) {
        for (int i = 0; i < PLAYBACK_SPEEDS.length; i++) {
            if (Math.abs(PLAYBACK_SPEEDS[i] - speed) < 0.01f) {
                return i;
            }
        }
        return -1;
    }



    /**
     * 启动进度更新器
     */
    private void startProgressUpdater() {
        if (mIsUpdatingProgress) return;

        mIsUpdatingProgress = true;
        mProgressUpdater = new Runnable() {
            @Override
            public void run() {
                if (mIsUpdatingProgress && !mIsUserSeeking) {
                    updateProgress();
                    mProgressHandler.postDelayed(this, 500); // 每500ms更新一次
                }
            }
        };
        mProgressHandler.post(mProgressUpdater);
    }

    /**
     * 停止进度更新器
     */
    private void stopProgressUpdater() {
        mIsUpdatingProgress = false;
        if (mProgressUpdater != null) {
            mProgressHandler.removeCallbacks(mProgressUpdater);
            mProgressUpdater = null;
        }
    }

    /**
     * 启动播放状态同步器
     */
    private void startStatusSync() {
        if (mIsStatusSyncEnabled) return;

        mIsStatusSyncEnabled = true;
        mStatusSyncRunnable = new Runnable() {
            @Override
            public void run() {
                if (mIsStatusSyncEnabled) {
                    // 检查播放状态并更新按钮图标
                    updatePlayPauseButtonIcon(false); // 不使用动画，避免频繁动画
                    mStatusSyncHandler.postDelayed(this, 1000); // 每秒检查一次
                }
            }
        };
        mStatusSyncHandler.post(mStatusSyncRunnable);
        Log.d(TAG, "播放状态同步器已启动");
    }

    /**
     * 停止播放状态同步器
     */
    private void stopStatusSync() {
        mIsStatusSyncEnabled = false;
        if (mStatusSyncRunnable != null) {
            mStatusSyncHandler.removeCallbacks(mStatusSyncRunnable);
            mStatusSyncRunnable = null;
        }
        Log.d(TAG, "播放状态同步器已停止");
    }

    /**
     * 更新播放进度
     */
    private void updateProgress() {
        long position = getCurrentPosition();
        long duration = getDuration();

        if (duration > 0 && mProgressSeekBar != null) {
            int progress = (int) (position * 100 / duration);
            mProgressSeekBar.setProgress(progress);
            updateTimeDisplay(position, duration);
        }
    }

    /**
     * 更新时间显示
     */
    private void updateTimeDisplay(long position, long duration) {
        String currentTime = formatTime(position);
        String totalTime = formatTime(duration);

        if (mCurrentTimeDisplay != null) {
            mCurrentTimeDisplay.setText(currentTime);
        }

        if (mTotalTimeDisplay != null) {
            mTotalTimeDisplay.setText(totalTime);
        }
    }

    /**
     * 格式化时间显示
     */
    private String formatTime(long timeMs) {
        if (timeMs < 0) return "00:00";

        long seconds = timeMs / 1000;
        long minutes = seconds / 60;
        long hours = minutes / 60;

        seconds = seconds % 60;
        minutes = minutes % 60;

        if (hours > 0) {
            return String.format("%02d:%02d:%02d", hours, minutes, seconds);
        } else {
            return String.format("%02d:%02d", minutes, seconds);
        }
    }

    /**
     * 设置视频系统
     */
    private void setupVideoSystem() {
        // 创建TpVideoSystem实例（仅用于视频播放功能，不初始化相机）
        TpVideoConfig config = TpVideoConfig.createDefault1080P();
        mVideoSystem = new TpVideoSystem((AppCompatActivity) getContext(), config);

        // 设置TextureView监听器
        mTextureView.setSurfaceTextureListener(new TextureView.SurfaceTextureListener() {
            @Override
            public void onSurfaceTextureAvailable(@NonNull SurfaceTexture surfaceTexture, int width, int height) {
                Log.d(TAG, "Surface可用，尺寸: " + width + "x" + height);

                // 释放之前的Surface
                if (mCurrentSurface != null) {
                    Log.d(TAG, "释放之前的Surface");
                    mCurrentSurface.release();
                    mCurrentSurface = null;
                }

                // 创建新的Surface
                mCurrentSurface = new Surface(surfaceTexture);
                mIsInitialized = true;

                // 如果已经设置了视频路径，则加载视频
                if (mVideoPath != null) {
                    loadVideo();
                }
            }

            @Override
            public void onSurfaceTextureSizeChanged(@NonNull SurfaceTexture surfaceTexture, int width, int height) {
                Log.d(TAG, "Surface尺寸变化: " + width + "x" + height);
            }

            @Override
            public boolean onSurfaceTextureDestroyed(@NonNull SurfaceTexture surfaceTexture) {
                Log.d(TAG, "Surface销毁");
                releaseVideoSystem();

                // 释放Surface
                if (mCurrentSurface != null) {
                    mCurrentSurface.release();
                    mCurrentSurface = null;
                }

                mIsInitialized = false;
                return true;
            }

            @Override
            public void onSurfaceTextureUpdated(@NonNull SurfaceTexture surfaceTexture) {
                // 视频帧更新
            }
        });

        // 设置视频系统监听器
        mVideoSystem.setListener(new TpVideoSystem.TpVideoSystemAdapter() {
            @Override
            public void onVideoPlaybackStarted(String videoPath) {
                Log.d(TAG, "视频播放开始: " + videoPath);
                // 播放开始时更新按钮状态
                post(() -> updatePlayPauseButtonIcon(true));
            }

            @Override
            public void onVideoPlaybackCompleted(String videoPath) {
                Log.d(TAG, "视频播放完成: " + videoPath);
                // 播放完成时更新按钮状态
                post(() -> updatePlayPauseButtonIcon(true));
            }

            @Override
            public void onVideoPlaybackStopped() {
                Log.d(TAG, "视频播放停止");
                // 播放停止时更新按钮状态
                post(() -> updatePlayPauseButtonIcon(true));
            }


            @Override
            public void onError(String errorMessage) {
                Log.e(TAG, "TpVideoSystem播放错误: " + errorMessage);

                // 检查是否是Surface相关错误
                if (errorMessage.contains("Surface") || errorMessage.contains("MediaCodec") ||
                        errorMessage.contains("configure") || errorMessage.contains("-22")) {
                    Log.e(TAG, "检测到Surface冲突错误，尝试重新创建Surface");

                    // 尝试重新创建Surface
                    post(() -> {
                        recreateSurface();
                        Toast.makeText(getContext(), "Surface冲突，正在重试...", Toast.LENGTH_SHORT).show();
                    });
                } else {
                    Toast.makeText(getContext(), "播放错误: " + errorMessage, Toast.LENGTH_LONG).show();
                }

                // 错误时也更新按钮状态
                post(() -> updatePlayPauseButtonIcon(true));
            }
        });

        // 控制界面回调已移除

        Log.d(TAG, "视频系统设置完成");
    }

    /**
     * 重新创建Surface（用于解决Surface冲突问题）
     */
    private void recreateSurface() {
        Log.d(TAG, "开始重新创建Surface");

        try {
            // 停止当前播放
            mVideoSystem.releaseVideo();

            // 释放当前Surface
            if (mCurrentSurface != null) {
                mCurrentSurface.release();
                mCurrentSurface = null;
            }

            // 重新创建Surface
            SurfaceTexture surfaceTexture = mTextureView.getSurfaceTexture();
            if (surfaceTexture != null) {
                mCurrentSurface = new Surface(surfaceTexture);
                Log.d(TAG, "Surface重新创建成功: " + mCurrentSurface);

                // 延迟重新加载视频，确保Surface完全就绪
                postDelayed(() -> {
                    if (mVideoPath != null) {
                        loadVideo();
                    }
                }, 200);
            } else {
                Log.e(TAG, "SurfaceTexture为null，无法重新创建Surface");
            }

        } catch (Exception e) {
            Log.e(TAG, "重新创建Surface时发生异常", e);
        }
    }

    /**
     * 释放视频系统资源
     */
    private void releaseVideoSystem() {
        // 停止视频播放
        Log.d(TAG, "停止视频播放");
        mVideoSystem.releaseVideo();

        // 注意：不调用mVideoSystem.release()，因为我们只是使用其视频播放功能
        // TpVideoSystem实例可以继续存在，供后续使用

        mIsInitialized = false;
        Log.d(TAG, "视频播放器资源已释放");
    }

    // ==================== 公共API ====================

    /**
     * 设置视频文件路径
     *
     * @param path 视频文件路径
     */
    public void setVideoPath(String path) {
        mVideoPath = path;
        Log.d(TAG, "设置视频路径: " + path);

        // 如果系统已初始化，立即加载视频
        if (mVideoSystem != null && mIsInitialized) {
            loadVideo();
        }
    }

    /**
     * 加载视频
     */
    private void loadVideo() {
        if (mVideoPath == null || mVideoPath.trim().isEmpty()) {
            Log.w(TAG, "视频路径为空，无法加载");
            return;
        }

        if (mTextureView == null || !mTextureView.isAvailable()) {
            Log.w(TAG, "TextureView未就绪，无法加载视频");
            return;
        }

        // 先停止之前的播放
        Log.d(TAG, "停止之前的视频播放");
        mVideoSystem.releaseVideo();

        try {
            // 使用管理的Surface
            if (mCurrentSurface == null) {
                Log.e(TAG, "Surface未就绪，无法播放视频");
                Toast.makeText(getContext(), "视频表面未就绪", Toast.LENGTH_SHORT).show();
                return;
            }

            // 验证Surface状态
            if (!mCurrentSurface.isValid()) {
                Log.e(TAG, "Surface无效，重新创建");
                SurfaceTexture surfaceTexture = mTextureView.getSurfaceTexture();
                if (surfaceTexture != null) {
                    mCurrentSurface.release();
                    mCurrentSurface = new Surface(surfaceTexture);
                } else {
                    Log.e(TAG, "SurfaceTexture为null，无法重新创建Surface");
                    Toast.makeText(getContext(), "视频表面创建失败", Toast.LENGTH_SHORT).show();
                    return;
                }
            }

            Log.d(TAG, "使用Surface播放视频: " + mCurrentSurface + ", 路径: " + mVideoPath);

            // 使用简化的API开始播放
            boolean success = mVideoSystem.playVideo(mVideoPath, mCurrentSurface);

            if (success) {
                Log.d(TAG, "视频加载成功: " + mVideoPath);

                // 如果没有设置自动播放且没有待播放标志，则暂停
                if (!mAutoPlay && !mIsPendingPlay) {
                    mVideoSystem.pauseVideo();
                } else {
                    mIsPendingPlay = false;
                }
            } else {
                Log.e(TAG, "视频加载失败: " + mVideoPath);
                Toast.makeText(getContext(), "视频加载失败", Toast.LENGTH_SHORT).show();
            }
        } catch (Exception e) {
            Log.e(TAG, "加载视频时发生异常", e);
            Toast.makeText(getContext(), "视频加载异常: " + e.getMessage(), Toast.LENGTH_LONG).show();
        }
    }

    /**
     * 开始播放
     */
    public void play() {
        if (mVideoSystem.isVideoPlaying()) {
            return; // 已经在播放
        }

        if (mVideoPath != null) {
            if (mVideoSystem.getCurrentVideoPath() != null) {
                // 恢复播放
                mVideoSystem.resumeVideo();
                Log.d(TAG, "恢复播放");

                // 恢复播放后立即更新按钮状态
                postDelayed(() -> {
                    updatePlayPauseButtonIcon(true);
                    if (mVideoSystem.isVideoPlaying()) {
                        startProgressUpdater();
                        startStatusSync(); // 启动状态同步
                    }
                }, 50);
            } else {
                // 开始新的播放
                mIsPendingPlay = true;
                loadVideo();

                // 新播放需要更长的延迟等待加载完成
                postDelayed(() -> {
                    updatePlayPauseButtonIcon(true);
                    if (mVideoSystem.isVideoPlaying()) {
                        startProgressUpdater();
                        startStatusSync(); // 启动状态同步
                    }
                }, 200);
            }
        } else {
            Log.w(TAG, "无法播放：没有设置视频路径");
            Toast.makeText(getContext(), "请先设置视频路径", Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * 暂停播放
     */
    public void pause() {
        mVideoSystem.pauseVideo();
        mIsPendingPlay = false;
        stopProgressUpdater();

        // 暂停后立即更新按钮状态
        postDelayed(() -> updatePlayPauseButtonIcon(true), 50);
        stopStatusSync(); // 停止状态同步
        Log.d(TAG, "暂停播放");
    }

    /**
     * 停止播放
     */
    public void stop() {
        mVideoSystem.releaseVideo();
        mIsPendingPlay = false;
        mCurrentSpeed = 1.0f; // 重置倍速
        stopProgressUpdater();
        stopStatusSync(); // 停止状态同步

        // 立即更新按钮状态
        post(() -> {
            updatePlayPauseButtonIcon(true);
            if (!mIsUserSeeking) {
                updateProgress();
            }
        });
        Log.d(TAG, "停止播放");
    }

    /**
     * 跳转到指定位置
     *
     * @param position 目标位置（毫秒）
     */
    public void seekTo(long position) {
        mVideoSystem.seekVideoTo(position);
        Log.d(TAG, "跳转到位置: " + position + "ms");
    }

    /**
     * 获取当前播放状态
     *
     * @return true表示正在播放，false表示已暂停
     */
    public boolean isPlaying() {
        return mVideoSystem.isVideoPlaying();
    }

    /**
     * 获取当前播放位置
     *
     * @return 当前位置（毫秒）
     */
    public long getCurrentPosition() {
        return mVideoSystem.getCurrentVideoPosition();
    }

    /**
     * 获取视频总时长
     *
     * @return 总时长（毫秒）
     */
    public long getDuration() {
        return mVideoSystem.getVideoDuration();
    }

    // ===== 高级播放控制API =====

    /**
     * 设置播放速度
     * <p>
     * 支持变速播放功能，常用于快速浏览或慢动作分析。
     * </p>
     *
     * @param speed 播放速度倍率（0.25f - 2.0f），1.0f表示正常速度
     */
    public void setPlaybackSpeed(float speed) {
        boolean success = mVideoSystem.setCurrentVideoPlaybackSpeed(speed);
        if (success) {
            mCurrentSpeed = speed;
            Log.d(TAG, "设置播放速度: " + speed + "x");
        } else {
            Log.w(TAG, "无法设置播放速度：视频未加载");
            Toast.makeText(getContext(), "无法设置播放速度：视频未加载", Toast.LENGTH_SHORT).show();
        }
    }

    /**
     * 获取当前播放速度
     *
     * @return 当前播放速度倍率
     */
    public float getPlaybackSpeed() {
        return mVideoSystem.getCurrentVideoPlaybackSpeed();
    }

    /**
     * 逐帧播放
     * <p>
     * 解码并显示下一帧，然后暂停。适用于精确的帧分析。
     * </p>
     */
    public void stepFrame() {
        boolean success = mVideoSystem.stepCurrentVideoFrame();
        if (success) {
            Log.d(TAG, "逐帧播放");
        } else {
            Log.w(TAG, "无法逐帧播放：视频未加载");
        }
    }

    /**
     * 相对跳转
     * <p>
     * 基于当前位置进行时间偏移跳转。
     * </p>
     *
     * @param deltaMs 时间偏移量（毫秒），正数前进，负数后退
     */
    public void seekRelative(long deltaMs) {
        boolean success = mVideoSystem.seekCurrentVideoRelative(deltaMs);
        if (success) {
            Log.d(TAG, "相对跳转: " + deltaMs + "ms");
        } else {
            Log.w(TAG, "无法相对跳转：视频未加载");
        }
    }


    /**
     * 检查播放是否已完成
     *
     * @return true表示播放完成
     */
    public boolean isPlaybackCompleted() {
        return mVideoSystem.isCurrentVideoPlaybackCompleted();
    }

    /**
     * 重置到开始位置
     * <p>
     * 将播放位置重置到视频开头。
     * </p>
     */
    public void resetToStart() {
        boolean success = mVideoSystem.resetCurrentVideoToStart();
        if (success) {
            Log.d(TAG, "重置到开始位置");
        } else {
            Log.w(TAG, "无法重置：视频未加载");
        }
    }

    // ===== 便捷方法 =====

    /**
     * 快进指定秒数
     * 如果剩余时间不足，则跳转到视频末尾
     *
     * @param seconds 快进秒数
     */
    public void fastForward(int seconds) {
        long currentPosition = getCurrentPosition();
        long duration = getDuration();
        long targetPosition = currentPosition + (seconds * 1000L);

        if (duration > 0) {
            if (targetPosition >= duration) {
                // 如果目标位置超过视频长度，跳转到真正的末尾
                seekTo(duration); // 直接跳到真正的终点
                Log.d(TAG, "快进到视频末尾");

                // 强制更新进度条到100%，因为getCurrentPosition可能不准确
                forceUpdateProgressToEnd(duration);
            } else {
                // 正常快进
                mVideoSystem.seekCurrentVideoRelative(seconds * 1000L);
                Log.d(TAG, "快进 " + seconds + " 秒");

                // 立即更新进度条和时间显示（无论是否在播放）
                updateProgressAndTime();
            }
        }
    }

    /**
     * 快退指定秒数
     * 如果当前时间不足，则跳转到视频开始
     *
     * @param seconds 快退秒数
     */
    public void fastRewind(int seconds) {
        long currentPosition = getCurrentPosition();
        long targetPosition = currentPosition - (seconds * 1000L);

        if (targetPosition <= 0) {
            // 如果目标位置小于0，跳转到开始
            seekTo(0);
            Log.d(TAG, "快退到视频开始");
        } else {
            // 正常快退
            mVideoSystem.seekCurrentVideoRelative(-seconds * 1000L);
            Log.d(TAG, "快退 " + seconds + " 秒");
        }

        // 立即更新进度条和时间显示（无论是否在播放）
        updateProgressAndTime();
    }

    /**
     * 立即更新进度条和时间显示
     * 用于在暂停状态下同步UI
     */
    private void updateProgressAndTime() {
        // 延迟一小段时间确保视频系统已经完成跳转
        postDelayed(() -> {
            long currentPosition = getCurrentPosition();
            long duration = getDuration();

            if (duration > 0) {
                // 更新进度条
                int progress = (int) (currentPosition * 100 / duration);
                if (mProgressSeekBar != null && !mIsUserSeeking) {
                    mProgressSeekBar.setProgress(progress);
                }

                // 更新时间显示
                updateTimeDisplay(currentPosition, duration);

                Log.d(TAG, "更新进度: " + progress + "%, 时间: " + formatTime(currentPosition) + "/" + formatTime(duration));
            }
        }, 100); // 延迟100ms确保跳转完成
    }

    /**
     * 强制更新进度条到100%
     * 用于快进到末尾时，因为getCurrentPosition可能不准确
     */
    private void forceUpdateProgressToEnd(long duration) {
        postDelayed(() -> {
            if (duration > 0) {
                // 强制设置进度条到100%
                if (mProgressSeekBar != null && !mIsUserSeeking) {
                    mProgressSeekBar.setProgress(100);
                }

                // 更新时间显示为终点时间
                updateTimeDisplay(duration, duration);

                Log.d(TAG, "强制更新进度到终点: 100%, 时间: " + formatTime(duration) + "/" + formatTime(duration));
            }
        }, 150); // 稍微延迟确保seekTo完成
    }


    // ===== 控制界面API =====

    /**
     * 设置是否显示播放控制界面
     *
     * @param show true显示控制界面，false隐藏控制界面
     */
    public void setShowControls(boolean show) {
        mShowControls = show;
        if (show) {
            showControls();
        } else {
            hideControls();
        }
    }

    /**
     * 检查控制界面是否可见
     *
     * @return true表示控制界面可见
     */
    public boolean isControlsVisible() {
        return mControlsVisible;
    }



    /**
     * 显示控制界面
     */
    private void showControls() {
        if (!mControlsVisible && mControlsView != null) {
            mControlsVisible = true;
            mControlsView.setVisibility(View.VISIBLE);
            mControlsView.animate().alpha(1.0f).setDuration(200).start();
        }
        scheduleHideControls();
    }

    /**
     * 隐藏控制界面
     */
    private void hideControls() {
        if (mControlsVisible && mControlsView != null) {
            mControlsVisible = false;
            mControlsView.animate()
                    .alpha(0.0f)
                    .setDuration(10)
                    .withEndAction(() -> {
                        if (mControlsView != null) {
                            mControlsView.setVisibility(View.GONE);
                        }
                    })
                    .start();
        }
        cancelHideControls();
    }

    /**
     * 切换控制界面显示状态
     */
    private void toggleControlsVisibility() {
        if (mControlsVisible) {
            hideControls();
        } else {
            showControls();
        }
    }

    /**
     * 安排3秒后隐藏控制界面
     */
    private void scheduleHideControls() {
        cancelHideControls();
        mControlsHandler.postDelayed(mHideControlsRunnable, CONTROLS_HIDE_DELAY);
    }

    /**
     * 取消隐藏控制界面的定时器
     */
    private void cancelHideControls() {
        mControlsHandler.removeCallbacks(mHideControlsRunnable);
    }

    /**
     * 重置控制界面隐藏定时器
     */
    private void resetControlsTimer() {
        if (mControlsVisible) {
            scheduleHideControls();
        }
    }


    // ==================== 样式定制API ====================

    /**
     * 获取内部的TpVideoSystem实例（供高级用户使用）
     *
     * @return TpVideoSystem实例
     */
    public TpVideoSystem getVideoSystem() {
        return mVideoSystem;
    }

    /**
     * 获取内部的TpTextureView实例
     *
     * @return TpTextureView实例
     */
    public TpTextureView getTextureView() {
        return mTextureView;
    }

    @Override
    protected void onDetachedFromWindow() {
        super.onDetachedFromWindow();
        stopProgressUpdater();
        stopStatusSync();
        cancelHideControls();
    }

    // ==================== 视频导航API ====================

    /**
     * 视频播放器监听器接口
     */
    public interface VideoPlayerListener {
        /**
         * 请求播放上一个视频
         */
        void onPreviousVideo();

        /**
         * 请求播放下一个视频
         */
        void onNextVideo();
    }

    /**
     * 设置视频播放器监听器
     *
     * @param listener 监听器实例
     */
    public void setVideoPlayerListener(VideoPlayerListener listener) {
        mVideoPlayerListener = listener;
    }

    /**
     * 获取视频播放器监听器
     *
     * @return 当前监听器实例
     */
    public VideoPlayerListener getVideoPlayerListener() {
        return mVideoPlayerListener;
    }

}