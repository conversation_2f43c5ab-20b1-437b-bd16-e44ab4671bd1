<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.android.rockchip.mediacodecnew"
    android:sharedUserId="android.uid.system"
    android:versionCode="1"
    android:versionName="1.0" >

    <uses-sdk
        android:minSdkVersion="31"
        android:targetSdkVersion="35" />

    <!-- 现有权限 -->
    <uses-permission android:name="android.permission.CAMERA" />
    <!-- 添加开机自启动权限 -->
    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission
        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
        android:maxSdkVersion="32" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
    <!-- 确保有以下权限 -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />

    <!-- &lt;!&ndash; 屏幕录制相关权限 &ndash;&gt; -->
    <!-- <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" /> -->
    <!-- <uses-permission android:name="android.permission.FOREGROUND_SERVICE" /> -->
    <!-- <uses-permission android:name="android.permission.CAPTURE_VIDEO_OUTPUT" -->
    <!-- tools:ignore="ProtectedPermissions" /> -->

    <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
    <uses-permission android:name="android.permission.CHANGE_WIFI_STATE" />
    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
    <uses-permission android:name="android.permission.TETHER_PRIVILEGED" />
    <uses-permission android:name="android.permission.WRITE_SETTINGS" />
    <uses-permission android:name="android.permission.MANAGE_WIFI_HOTSPOT" />
    <uses-permission android:name="android.permission.CONNECTIVITY_INTERNAL" />
    <uses-permission android:name="android.permission.START_TETHERING" />

    <!-- TV模式所需权限 -->
    <uses-permission android:name="com.android.providers.tv.permission.READ_EPG_DATA" />

    <!-- 声明TV功能 -->
    <uses-feature
        android:name="android.software.live_tv"
        android:required="false" />

    <!-- 网络相关权限 -->
    <uses-permission android:name="android.permission.NEARBY_WIFI_DEVICES" />

    <!-- 要求后置摄像头 -->
    <uses-feature android:name="android.hardware.camera" />

    <permission
        android:name="com.android.rockchip.mediacodecnew.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
        android:protectionLevel="signature" />

    <uses-permission android:name="com.android.rockchip.mediacodecnew.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />

    <application
        android:allowBackup="true"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
        android:dataExtractionRules="@xml/data_extraction_rules"
        android:debuggable="true"
        android:extractNativeLibs="false"
        android:fullBackupContent="@xml/backup_rules"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:testOnly="true"
        android:theme="@style/Theme.MediacodecNew" >

        <!-- MainActivity 作为主启动Activity -->
        <activity
            android:name="com.android.rockchip.camera2.integrated.MainActivity"
            android:exported="true" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <!-- 添加 VideoDecoderActivity 的声明 -->
        <activity android:name="com.android.rockchip.camera2.separated.VideoDecoderActivity" />
        <!-- 添加 MediaBrowserActivity 的声明 -->
        <activity android:name="com.android.rockchip.camera2.separated.MediaBrowserActivity" />
        <!-- 添加 ImageViewerActivity 的声明 -->
        <activity android:name="com.android.rockchip.camera2.separated.ImageViewerActivity" />

        <!-- integrated版本的Activity声明 -->
        <activity android:name="com.android.rockchip.camera2.integrated.browser.MediaBrowserActivity" />
        <activity android:name="com.android.rockchip.camera2.integrated.browser.ImageViewerActivity" />
        <activity android:name="com.android.rockchip.camera2.integrated.browser.TpVideoPlayerActivity" />
        <activity android:name="com.android.rockchip.camera2.integrated.browser.ImageVideoCompareActivity" />
        <activity android:name="com.android.rockchip.camera2.separated.TpVideoPlayerActivity" />

        <service
            android:name="com.touptek.video.internal.rtsp.service.RTSPService"
            android:enabled="true"
            android:exported="false"
            android:foregroundServiceType="mediaProjection"
            android:stopWithTask="false" />

        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="com.android.rockchip.mediacodecnew.androidx-startup"
            android:exported="false" >
            <meta-data
                android:name="androidx.emoji2.text.EmojiCompatInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
                android:value="androidx.startup" />
        </provider>

        <receiver
            android:name="androidx.profileinstaller.ProfileInstallReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.DUMP" >
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
            </intent-filter>
        </receiver>
    </application>

</manifest>